# 注册管理模块设计文档

## 1. 模块概述

注册管理模块是系统的核心组件之一，负责处理用户注册、设备注册、证书管理以及相关数据查询等功能。该模块实现了完整的用户和设备生命周期管理，包括注册、查询、更新、以及证书和密钥的绑定管理。

### 1.1 位置查询接口现状总结

注册管理模块目前已完整实现了用户/设备位置查询相关的接口功能，包括：

#### 1.1.1 已实现功能

1. **位置分布查询接口** (`GET /api/get/location`)

   - ✅ 已完全实现并部署
   - ✅ 支持查询所有用户和网关设备的位置信息
   - ✅ 自动过滤未设置位置信息的记录
   - ✅ 返回标准化的JSON格式数据
   - ✅ 包含完整的参数验证和错误处理
2. **登录记录查询接口** (`GET /api/get/login`)

   - ✅ 已完全实现并部署
   - ✅ 支持按时间范围查询登录活动记录
   - ✅ 按网关分组展示用户登录信息
   - ✅ 自动补充网关位置信息
   - ✅ 支持ISO 8601时间格式输出
3. **数据库支持**

   - ✅ 用户表包含 `user_location` 字段
   - ✅ 设备表包含 `gateway_location` 字段
   - ✅ 登录事件表 `login_events` 已创建并投入使用
   - ✅ 相关索引已优化，支持高效查询
4. **仓库层实现**

   - ✅ `LoginEventRepository` 完整实现
   - ✅ 支持按网关查询登录事件
   - ✅ 支持按时间范围查询
   - ✅ 提供聚合查询方法

#### 1.1.2 接口特点

- **统一的API设计**：遵循系统API设计规范，使用 `start_time` 和 `end_time` 参数
- **完整的错误处理**：包含参数验证、数据库连接检查、查询异常处理
- **性能优化**：通过数据库索引和查询优化提升响应速度
- **数据过滤**：自动过滤空位置信息，确保返回有效数据
- **日志记录**：完整的操作日志记录，便于调试和监控

#### 1.1.3 技术实现

- **路由配置**：在 `regist/router/router.go` 中完整配置
- **处理器实现**：在 `regist/handler/location.go` 中实现业务逻辑
- **数据模型**：使用标准的GORM模型定义
- **仓库模式**：通过仓库模式实现数据访问抽象
- **响应格式化**：提供专门的响应构建函数

#### 1.1.4 当前状态

位置查询相关的所有接口均已完成开发、测试并投入生产使用，功能完整且稳定运行。

### 1.2 API 接口字段修复总结

#### 1.2.1 修复背景

在 2025-06-17 的系统检查中发现，部分 API 接口存在字段缺失问题：
- search 模块设备查询接口缺少 `is_superior_forbidden` 字段
- 用户相关接口缺少 `mac_address` 字段支持

#### 1.2.2 修复内容

**1. 设备查询接口 is_superior_forbidden 字段修复**

- ✅ **问题**：设备列表查询接口 (`GET /search/devices`) 直接返回数据库模型，未使用响应转换函数
- ✅ **修复**：修改 `GetDevices` 函数，使用 `convertDeviceModelToResponse` 函数进行响应转换
- ✅ **影响接口**：
  - `GET /search/devices` - 设备列表查询
  - `GET /search/device` - 单个设备查询（已正常）
- ✅ **字段功能**：动态计算设备的上级设备链路中是否存在 auth_status 为 "forbidden" 的设备

**2. 用户接口 mac_address 字段修复**

- ✅ **问题**：`mac_address` 字段使用了 `omitempty` JSON 标签，导致空值时不显示
- ✅ **修复**：移除 `UserResponse` 结构体中 `mac_address` 字段的 `omitempty` 标签
- ✅ **影响接口**：
  - `GET /search/users` - 用户列表查询
  - `GET /search/user` - 单个用户查询
  - `POST /regist/users` - 用户注册（已支持）
  - `PUT /update/user` - 用户更新（已支持）
- ✅ **字段功能**：支持用户设备 MAC 地址的存储、验证和查询

#### 1.2.3 修复验证

通过自动化验证脚本确认所有接口字段修复完成：

- ✅ 设备列表查询接口包含 `is_superior_forbidden` 字段
- ✅ 设备单个查询接口包含 `is_superior_forbidden` 字段
- ✅ 用户列表查询接口包含 `mac_address` 字段
- ✅ 用户列表查询接口包含 `is_superior_forbidden` 字段
- ✅ 用户单个查询接口包含 `mac_address` 字段
- ✅ 用户单个查询接口包含 `is_superior_forbidden` 字段

#### 1.2.4 技术实现细节

**响应结构体定义**：
```go
// UserResponse 用户查询响应结构体
type UserResponse struct {
    // ... 其他字段 ...
    MacAddress          string  `json:"mac_address"`             // MAC地址
    IsSuperiorForbidden bool    `json:"is_superior_forbidden"`   // 上级设备冻结状态
}

// DeviceResponse 设备查询响应结构体
type DeviceResponse struct {
    // ... 其他字段 ...
    IsSuperiorForbidden bool   `json:"is_superior_forbidden"`      // 上级设备冻结状态
}
```

**字段计算逻辑**：
- `is_superior_forbidden`：通过 `CheckSuperiorDeviceFrozenStatus` 和 `CheckGatewayDeviceFrozenStatus` 方法动态计算
- `mac_address`：直接从数据库模型映射，支持格式验证和唯一性检查

#### 1.2.5 兼容性保证

- ✅ 保持现有 API 接口的完全向后兼容性
- ✅ 新增字段不影响现有客户端的正常使用
- ✅ 响应格式和数据结构保持一致
- ✅ 错误处理和状态码保持不变

### 1.3 设备注册接口字段修复总结

#### 1.3.1 修复背景

在 2025-06-18 的系统检查中发现，设备注册接口存在以下问题：
- 设备注册接口缺少 `hardware_fingerprint` 字段支持
- `register_ip` 字段的业务逻辑错误，当前使用客户端IP而非目标服务器IP

#### 1.3.2 修复内容

**1. hardware_fingerprint 字段修复**

- ✅ **问题**：设备注册请求结构体 `DeviceRegisterRequest` 中缺少 `hardware_fingerprint` 字段
- ✅ **修复**：在 `DeviceRegisterRequest` 结构体中添加 `hardware_fingerprint` 字段定义
- ✅ **修复**：在设备创建逻辑中添加硬件指纹字段的处理
- ✅ **影响接口**：
  - `POST /regist/devices` - 设备注册接口
- ✅ **字段功能**：支持设备硬件指纹的存储和管理，用于设备唯一性识别

**2. register_ip 字段业务逻辑修复**

- ✅ **问题**：原实现使用 `c.ClientIP()` 自动获取客户端IP，不符合业务需求
- ✅ **修复**：修改为从请求中获取目标服务器IP地址，表示设备要连接的目标服务器IP
- ✅ **实现**：在 `DeviceRegisterRequest` 结构体中添加 `register_ip` 必填字段
- ✅ **修复**：在设备创建逻辑中使用 `request.RegisterIP` 而不是 `c.ClientIP()`
- ✅ **说明**：该字段现在表示设备发起初始化注册时的目标IP地址，由前端明确提供

#### 1.3.3 修复验证

通过代码审查确认所有字段修复完成：

- ✅ 设备注册接口支持 `hardware_fingerprint` 字段输入
- ✅ 设备注册接口正确处理和存储硬件指纹信息
- ✅ 设备注册接口使用请求提供的目标服务器IP地址
- ✅ 响应结构体包含完整的设备信息字段

#### 1.3.4 技术实现细节

**请求结构体定义**：
```go
// DeviceRegisterRequest 设备注册请求结构体
type DeviceRegisterRequest struct {
    // ... 其他字段 ...
    RegisterIP          string `json:"register_ip" binding:"required"`      // 设备注册目标IP地址，必填字段
    HardwareFingerprint string `json:"hardware_fingerprint"`                // 设备硬件指纹，可选字段
    // ... 其他字段 ...
}
```

**字段处理逻辑**：
- `hardware_fingerprint`：从请求中获取并直接存储到数据库
- `register_ip`：从请求中获取目标服务器IP地址，不再使用 `c.ClientIP()`

#### 1.3.5 向后兼容性

- ⚠️ **重要变更**：`register_ip` 字段现在为必填字段，需要前端调用方更新
- ✅ `hardware_fingerprint` 字段为可选字段，不影响现有客户端
- ✅ 响应格式和数据结构保持一致
- ✅ 现有设备注册流程需要适配新的字段要求

## 2. 代码结构

### 2.1 核心组件

#### 2.1.1 路由组件

`router` 包含注册模块的路由设置，负责定义API端点并将请求分发到对应的处理函数。

```go
// SetupRouter 设置注册模块路由
func SetupRouter(r *gin.Engine) {
    // ...各种路由注册...
}
```

#### 2.1.2 处理器组件

`handler` 包提供了处理各种请求的函数，是注册模块的业务逻辑核心：

- `user.go`: 处理用户相关操作（注册、更新、查询）
- `device.go`: 处理设备相关操作（注册、更新、查询）
- `cert.go`: 处理证书和密钥绑定操作
- `search.go`: 处理查询操作的专门函数
- `location.go`: 处理位置查询和登录记录查询操作
- `response.go`: 定义响应格式化结构体和函数

#### 2.1.3 证书管理组件

`certs` 目录管理用户和设备的证书文件和密钥文件的存储：

- `certs`: 存储证书文件
- `keys`: 存储密钥文件

### 2.2 辅助组件

- **日志记录器**: 使用系统通用的日志组件记录操作日志
- **数据库仓库**: 通过仓库模式实现数据访问
- **请求日志中间件**: 记录请求与响应信息，支持调试追踪

### 2.3 主要功能函数索引

#### 2.3.1 路由设置函数

| 函数名                   | 文件      | 行号 | 描述                                    |
| ------------------------ | --------- | ---- | --------------------------------------- |
| SetupRouter              | router.go | ~193 | 设置注册模块所有路由                    |
| handleUserUpdateOptions  | router.go | ~351 | 处理用户更新的OPTIONS请求，解决CORS问题 |
| requestAndResponseLogger | router.go | ~51  | 请求和响应日志记录中间件                |

#### 2.3.2 用户管理函数

| 函数名       | 文件              | 行号 | 描述                     |
| ------------ | ----------------- | ---- | ------------------------ |
| RegisterUser | handler/user.go   | ~44  | 处理用户注册请求         |
| GetUsers     | handler/user.go   | ~245 | 处理获取所有用户的请求   |
| GetUserByID  | handler/search.go | ~15  | 处理根据ID查询用户的请求 |
| UpdateUser   | handler/user.go   | ~300 | 处理用户信息更新请求     |

#### 2.3.3 设备管理函数

| 函数名                | 文件              | 行号 | 描述                           |
| --------------------- | ----------------- | ---- | ------------------------------ |
| RegisterDevice        | handler/device.go | ~85  | 处理设备注册请求               |
| GetDevices            | handler/device.go | ~239 | 处理获取所有设备的请求         |
| GetDeviceByID         | handler/search.go | ~56  | 处理根据ID查询设备的请求       |
| GetDevicesReadyStatus | handler/search.go | ~133 | 处理查询所有设备就绪状态的请求 |
| GetDeviceReadyStatus  | handler/search.go | ~268 | 处理查询单个设备就绪状态的请求 |
| UpdateDevice          | handler/device.go | ~300 | 处理设备信息更新请求           |

#### 2.3.4 搜索接口函数

| 函数名              | 文件              | 行号 | 描述                     | 状态     |
| ------------------- | ----------------- | ---- | ------------------------ | -------- |
| SearchAll           | handler/search.go | -    | 处理综合搜索请求         | 🚧 待实现 |
| SearchUsersByName   | handler/search.go | -    | 处理用户名搜索请求       | 🚧 待实现 |
| SearchDevicesByName | handler/search.go | -    | 处理设备名搜索请求       | 🚧 待实现 |

#### 2.3.5 证书管理函数

| 函数名         | 文件            | 行号 | 描述             |
| -------------- | --------------- | ---- | ---------------- |
| BindUserCert   | handler/cert.go | ~27  | 处理用户证书绑定 |
| BindUserKey    | handler/cert.go | ~100 | 处理用户密钥绑定 |
| BindDeviceCert | handler/cert.go | ~248 | 处理设备证书绑定 |
| BindDeviceKey  | handler/cert.go | ~355 | 处理设备密钥绑定 |
| GetCertInfo    | handler/cert.go | ~462 | 获取证书信息     |

#### 2.3.6 位置查询和事件记录函数

| 函数名                  | 文件                      | 行号 | 描述                       |
| ----------------------- | ------------------------- | ---- | -------------------------- |
| GetLocationData         | handler/location.go       | ~17  | 处理位置查询接口           |
| GetEventRecords         | handler/event_records.go  | ~20  | 处理事件记录查询接口       |
| GetLoginRecordsCompat   | handler/event_records.go  | ~80  | 处理兼容性登录记录查询接口 |
| buildLocationResponse   | handler/location.go       | ~215 | 构建位置查询接口的响应数据 |

#### 2.3.7 数据库仓库函数

| 函数名              | 文件                                       | 行号 | 描述                        |
| ------------------- | ------------------------------------------ | ---- | --------------------------- |
| FindGatewayDevices  | database/repositories/device_repository.go | ~76  | 查找所有网关设备(类型1,2,3) |
| FindSoftwareDevices | database/repositories/device_repository.go | ~85  | 查找所有软件设备(类型4)     |

#### 2.3.8 辅助函数

| 函数名                       | 文件                | 行号 | 描述                               |
| ---------------------------- | ------------------- | ---- | ---------------------------------- |
| saveFileToDisk               | handler/cert.go     | ~590 | 将证书或密钥文件保存到磁盘         |
| convertUserModelToResponse   | handler/response.go | ~34  | 将用户模型转换为响应结构体         |
| convertDeviceModelToResponse | handler/response.go | ~99  | 将设备模型转换为响应结构体         |
| requestAndResponseLogger     | router.go           | ~78  | 请求和响应日志记录中间件           |
| generateRequestID            | router.go           | ~64  | 生成唯一请求ID                     |

## 3. 工作流程

### 3.1 用户注册流程

1. **接收请求**：接收包含用户信息的POST请求
2. **参数验证**：验证必填参数和格式
3. **唯一性检查**：检查用户ID和用户名是否已存在
4. **数据库操作**：创建用户记录
5. **关联处理**：如有必要，创建用户连接关系
6. **配置触发**：如用户属于网关设备，触发网关配置文件更新
7. **返回响应**：返回注册结果

### 3.2 设备注册流程

1. **接收请求**：接收包含设备信息的POST请求
2. **参数验证**：验证必填参数和格式
3. **唯一性检查**：检查设备ID和设备名称是否已存在
4. **类型验证**：对网关设备类型进行特殊验证
5. **数据库操作**：创建设备记录
6. **配置触发**：对于网关设备，触发配置文件生成
7. **返回响应**：返回注册结果

### 3.3 证书绑定流程

1. **身份验证**：验证用户/设备ID是否有效
2. **文件验证**：检查上传的文件大小和格式
3. **文件保存**：将证书/密钥文件保存到对应目录
4. **数据库更新**：更新用户/设备记录中的证书/密钥ID
5. **返回响应**：返回绑定结果

### 3.4 查询流程

1. **参数获取**：获取查询参数（ID或其他过滤条件）
2. **数据库查询**：根据条件从数据库获取记录
3. **响应格式化**：将数据库模型转换为响应结构体
4. **返回响应**：返回查询结果

## 4. 模块配置项

注册模块的相关配置项主要来自全局配置，包括：

### 4.1 认证模块配置

| 配置项                      | 说明                     | 默认值 |
| --------------------------- | ------------------------ | ------ |
| AuthModule.Enabled          | 是否启用认证模块         | true   |
| AuthModule.EnableUserAuth   | 是否启用用户认证功能     | true   |
| AuthModule.EnableDeviceAuth | 是否启用设备认证功能     | true   |
| AuthInterval                | 设备认证间隔时间（分钟） | 10     |

### 4.2 证书模块配置

| 配置项                   | 说明                 | 默认值               |
| ------------------------ | -------------------- | -------------------- |
| CertModule.Enabled       | 是否启用证书管理功能 | true                 |
| CertModule.CertDir       | 证书存储目录         | "regist/certs/certs" |
| CertModule.KeyDir        | 密钥存储目录         | "regist/certs/keys"  |
| CertModule.CertValidDays | 证书有效期（天）     | 365                  |

### 4.3 设备模块配置

| 配置项                           | 说明                 | 默认值 |
| -------------------------------- | -------------------- | ------ |
| DeviceModule.Enabled             | 是否启用设备管理模块 | true   |
| DeviceModule.EnableAutoRegister  | 是否启用自动注册     | true   |
| DeviceModule.EnableBatchRegister | 是否启用批量注册     | true   |
| DeviceModule.MaxBatchSize        | 最大批量注册数量     | 100    |

### 4.4 用户模块配置

| 配置项                           | 说明                 | 默认值 |
| -------------------------------- | -------------------- | ------ |
| UserModule.Enabled               | 是否启用用户管理模块 | true   |
| UserModule.EnableAutoRegister    | 是否启用自动注册     | true   |
| UserModule.EnableBatchRegister   | 是否启用批量注册     | true   |
| UserModule.EnableUserConnections | 是否启用用户连接关系 | true   |
| UserModule.MaxConnectionsPerUser | 每用户最大连接数     | 30     |

## 5. 数据库表结构

注册管理模块使用以下数据库表来存储和管理相关数据：

### 5.1 用户表 (users)

| 字段名               | 类型       | 说明                     | 索引       |
| -------------------- | ---------- | ------------------------ | ---------- |
| id                   | uint       | 自增主键                 | 主键       |
| username             | string     | 用户名，唯一             | 唯一索引   |
| password             | string     | 用户密码(加密存储)       | -          |
| user_id              | string     | 用户唯一标识             | 唯一索引   |
| user_type            | int        | 用户类型                 | 索引       |
| gateway_device_id    | string     | 用户所属网关设备ID       | 索引，外键 |
| online_duration      | int        | 在线时长(秒)             | -          |
| cert_id              | string     | 关联的证书ID             | 索引       |
| key_id               | string     | 关联的密钥ID             | 索引       |
| email                | string     | 用户邮箱                 | -          |
| permission_mask      | string     | 权限掩码                 | -          |
| last_login_timestamp | *time.Time | 最后登录时间             | -          |
| offline_timestamp    | *time.Time | 最近离线时间             | -          |
| login_ip             | string     | 最后登录IP               | -          |
| illegal_login_times  | *int       | 非法登录次数             | -          |
| auth_status          | string     | 认证状态(normal/blocked) | 索引       |
| login_status         | string     | 登录状态(online/offline) | 索引       |
| user_location        | string     | 用户位置信息             | -          |
| mac_address          | string     | 用户设备MAC地址          | 索引       |
| created_at           | time.Time  | 创建时间                 | 索引       |
| updated_at           | time.Time  | 更新时间                 | -          |

### 5.2 设备表 (devices)

| 字段名               | 类型      | 说明                          | 索引     |
| -------------------- | --------- | ----------------------------- | -------- |
| id                   | uint      | 自增主键                      | 主键     |
| device_name          | string    | 设备名称，唯一                | 唯一索引 |
| device_type          | int       | 设备类型(1-3:网关,4:安全接入) | 索引     |
| password             | string    | 设备密码(加密存储)            | -        |
| device_id            | string    | 设备唯一标识                  | 唯一索引 |
| superior_device_id   | string    | 上级设备ID                    | 索引     |
| online_duration      | int       | 在线时长(秒)                  | -        |
| cert_id              | string    | 关联的证书ID                  | 索引     |
| key_id               | string    | 关联的密钥ID                  | 索引     |
| register_ip          | string    | 设备注册目标IP地址            | -        |
| email                | string    | 关联邮箱                      | -        |
| hardware_fingerprint | string    | 设备硬件指纹                  | 索引     |
| anonymous_user       | string    | 匿名用户信息                  | -        |
| peak_cpu_usage       | int       | CPU峰值使用率                 | -        |
| peak_memory_usage    | int       | 内存峰值使用率                | -        |
| long_address         | string    | 网关设备长地址(IPv6)          | 索引     |
| short_address        | string    | 网关设备短地址(2字节)         | 索引     |
| ses_key              | string    | SES密钥                       | -        |
| gateway_location     | string    | 网关位置信息                  | -        |
| is_ready             | bool      | 设备就绪状态                  | 索引     |
| auth_status          | string    | 认证状态(normal/blocked)      | 索引     |
| login_status         | string    | 登录状态(online/offline)      | 索引     |
| created_at           | time.Time | 创建时间                      | 索引     |
| updated_at           | time.Time | 更新时间                      | -        |

### 5.3 用户连接表 (user_connects)

| 字段名          | 类型      | 说明         | 索引     |
| --------------- | --------- | ------------ | -------- |
| id              | uint      | 自增主键     | 主键     |
| user_id         | string    | 用户ID       | 联合索引 |
| connect_user_id | string    | 连接的用户ID | 联合索引 |
| created_at      | time.Time | 创建时间     | -        |

### 5.4 证书表 (certs)

| 字段名      | 类型       | 说明                  | 索引 |
| ----------- | ---------- | --------------------- | ---- |
| id          | uint       | 自增主键              | 主键 |
| entity_type | string     | 实体类型(user/device) | 索引 |
| entity_id   | string     | 实体ID                | 索引 |
| cert_path   | string     | 证书文件路径          | -    |
| key_path    | string     | 密钥文件路径          | -    |
| upload_time | time.Time  | 证书上传时间          | -    |
| created_at  | time.Time  | 创建时间              | -    |
| updated_at  | time.Time  | 更新时间              | -    |
| deleted_at  | *time.Time | 删除时间（软删除）    | -    |

### 5.5 登录事件表 (login_events)

| 字段名         | 类型      | 说明                          | 索引 |
| -------------- | --------- | ----------------------------- | ---- |
| id             | uint      | 自增主键                      | 主键 |
| entity_type    | string    | 实体类型("user"或"gateway")   | 索引 |
| entity_id      | string    | 实体ID(用户ID或设备ID)        | 索引 |
| gateway_id     | string    | 关联的网关ID                  | 索引 |
| operation_type | string    | 操作类型("online"或"offline") | 索引 |
| operation_time | time.Time | 操作发生时间                  | 索引 |
| login_result   | string    | 登录结果("success"或"failed") | 索引 |
| created_at     | time.Time | 记录创建时间                  | -    |
| updated_at     | time.Time | 记录更新时间                  | -    |

### 5.6 表关系

- **users 与 devices**: 用户表的 `gateway_device_id` 字段关联到设备表的 `device_id` 字段，表示用户所属的网关设备
- **users 与 user_connects**: 用户表的 `user_id` 字段与用户连接表的 `user_id` 和 `connect_user_id` 字段关联，表示用户之间的连接关系
- **users/devices 与 certs**: 通过 `entity_type` 和 `entity_id` 字段关联到证书表，`entity_type` 表示实体类型(user/device)，`entity_id` 对应用户ID或设备ID
- **login_events 与 users/devices**: 通过 `entity_type` 和 `entity_id` 字段关联，记录用户和设备的登录活动
- **login_events 与 devices**: 通过 `gateway_id` 字段关联到设备表，表示登录事件所属的网关设备

## 6. 接口详细说明

### 6.1 位置查询接口

#### 6.1.1 用户/设备位置分布查询接口

**HTTP 方法**: GET

**路径**: `/api/get/location`

**功能**：获取系统中所有用户、网关设备和软件设备的位置信息，用于地理分布可视化展示

**请求参数**：

| 参数名     | 类型   | 必填 | 说明                                |
| ---------- | ------ | ---- | ----------------------------------- |
| start_time | string | 是   | 起始时间，格式：YYYY-MM-DD HH:mm:ss |
| end_time   | string | 是   | 结束时间，格式：YYYY-MM-DD HH:mm:ss |

**时间范围说明**：

- 时间参数主要用于前端统一调用格式，实际返回当前所有用户、网关设备和软件设备的位置信息
- 接口会验证时间格式的有效性，但不会根据时间范围过滤数据

**cURL 请求示例**：

```bash
curl -X GET "http://localhost:8080/api/get/location?start_time=2023-08-01%2000:00:00&end_time=2023-08-01%2023:59:59"
```

**成功响应 (200 OK)**：

```json
{
  "user": [
    {
      "user_id": "U0000001",
      "user_location": "guangzhou"
    }
  ],
  "gateway": [
    {
      "gateway_id": "G00000001",
      "gateway_location": "guangzhou"
    }
  ],
  "software": [
    {
      "software_id": "S00000001",
      "software_location": "guangzhou"
    }
  ]
}
```

**响应体字段说明**：

| 字段                        | 类型   | 描述                                         |
| --------------------------- | ------ | -------------------------------------------- |
| user                        | Array  | 用户位置信息数组                             |
| user[].user_id              | String | 用户唯一标识                                 |
| user[].user_location        | String | 用户位置信息                                 |
| gateway                     | Array  | 网关设备位置信息数组                         |
| gateway[].gateway_id        | String | 网关设备唯一标识（设备类型为1、2、3的设备）  |
| gateway[].gateway_location  | String | 网关设备位置信息                             |
| software                    | Array  | 软件设备位置信息数组                         |
| software[].software_id      | String | 软件设备唯一标识（设备类型为4的设备）        |
| software[].software_location| String | 软件设备位置信息                             |

**失败响应 (400 Bad Request)**：

```json
{
  "code": 400,
  "message": "缺少必要的时间参数",
  "data": null
}
```

```json
{
  "code": 400,
  "message": "无效的开始时间格式，应为YYYY-MM-DD HH:mm:ss",
  "data": null
}
```

**失败响应 (500 Internal Server Error)**：

```json
{
  "code": 500,
  "message": "数据库连接失败",
  "data": null
}
```

**处理逻辑**：

1. 从数据库中查询所有用户的 `user_id` 和 `user_location` 字段
2. 从数据库中查询设备类型为1、2、3（网关设备）的 `device_id` 和 `gateway_location` 字段
3. 从数据库中查询设备类型为4（软件设备）的 `device_id` 和 `gateway_location` 字段
4. 过滤掉未设置位置信息的用户和设备
5. 组织数据为指定JSON格式并返回

#### 6.1.2 用户/网关登录记录查询接口

**查询路径**：GET /api/get/login

**功能**：获取系统中所有用户和网关的登录记录信息，包括上线和下线操作

**请求参数**：

| 参数名     | 类型   | 必填 | 说明                                |
| ---------- | ------ | ---- | ----------------------------------- |
| start_time | string | 是   | 起始时间，格式：YYYY-MM-DD HH:mm:ss |
| end_time   | string | 是   | 结束时间，格式：YYYY-MM-DD HH:mm:ss |

**时间范围限制**：

- 起始时间不能晚于结束时间
- 查询指定时间范围内的登录活动记录

**请求示例**：

```bash
GET /api/get/login?start_time=2023-08-01 00:00:00&end_time=2023-08-01 23:59:59
```

**响应示例**：

```json
[
  {
    "gateway_id": "G00000001",
    "gateway_location": "guangzhou",
    "gateway_operation_time": "2025-04-23T08:14:46+08:00",
    "gateway_operation_type": "online",
    "user": [
      {
        "user_id": "U0000001",
        "user_operation_time": "2025-04-23T08:14:46+08:00",
        "user_operation_type": "online"
      }
    ]
  }
]
```

**字段说明**：

- 根级数组项：
  - `gateway_id`: 网关设备唯一标识
  - `gateway_location`: 网关设备位置信息
  - `gateway_operation_time`: 网关操作时间（ISO 8601格式）
  - `gateway_operation_type`: 操作类型，值为 "online" 或 "offline"
- `user` 数组：
  - `user_id`: 用户唯一标识
  - `user_operation_time`: 用户操作时间（ISO 8601格式）
  - `user_operation_type`: 操作类型，值为 "online" 或 "offline"

**处理逻辑**：

1. 查询指定时间范围内的网关设备登录记录，包括登录状态变更为"online"或"offline"的记录
2. 对于每个网关设备，查询其下属用户的登录记录
3. 按照网关分组组织数据，每个网关项包含其自身的登录信息以及其下属用户的登录信息
4. 从设备表补充网关位置信息
5. 按照指定JSON格式返回数据

**数据来源**：

- **登录事件表 (login_events)**: 记录用户和网关的登录/登出事件
- **设备表 (devices)**: 提供网关位置信息
- **用户表 (users)**: 提供用户与网关的关联关系

### 6.2 用户管理接口

#### 6.2.1 用户注册

**HTTP 方法**: POST

**路径**: `/regist/users`

**功能**: 注册新用户到系统中

**请求参数**:

| 字段名              | 类型     | 必填 | 描述                                                    |
| ------------------- | -------- | ---- | ------------------------------------------------------- |
| user_name           | String   | 是   | 用户名，4-20字符                                        |
| pass_wd             | String   | 是   | 密码，最少8字符                                         |
| user_id             | String   | 是   | 用户唯一标识                                            |
| user_type           | Integer  | 是   | 用户类型                                                |
| gateway_device_id   | String   | 是   | 用户所属网关设备ID                                      |
| cert_id             | String   | 否   | 证书ID                                                  |
| key_id              | String   | 否   | 密钥ID                                                  |
| email               | String   | 否   | 邮箱                                                    |
| auth_status         | String   | 否   | 认证状态，默认"normal"                                  |
| login_status        | String   | 否   | 登录状态，默认"offline"                                 |
| user_connect_list   | Array    | 否   | 用户连接列表                                            |
| user_location       | String   | 否   | 用户位置信息                                            |
| mac_address         | String   | 否   | 用户设备MAC地址，格式：XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX |

**MAC地址字段说明**：
- 支持标准MAC地址格式：`AA:BB:CC:DD:EE:FF` 或 `AA-BB-CC-DD-EE-FF`
- 支持大小写字母
- 自动进行格式验证和唯一性检查
- 可选字段，允许为空

**cURL 请求示例**:

```bash
curl -X POST "http://localhost:8080/regist/users" \
  -H "Content-Type: application/json" \
  -d '{
    "user_name": "testuser",
    "pass_wd": "password123",
    "user_id": "U0000001",
    "user_type": 1,
    "gateway_device_id": "G00000001",
    "email": "<EMAIL>",
    "user_location": "guangzhou",
    "mac_address": "AA:BB:CC:DD:EE:FF"
  }'
```

**成功响应 (200 OK)**:

```json
{
  "code": 200,
  "message": "用户注册成功",
  "data": {
    "user_name": "testuser",
    "user_id": "U0000001",
    "user_type": 1,
    "gateway_device_id": "G00000001",
    "email": "<EMAIL>",
    "auth_status": "normal",
    "login_status": "offline",
    "user_location": "guangzhou",
    "mac_address": "AA:BB:CC:DD:EE:FF",
    "created_at": "2025-06-18T10:30:00Z"
  }
}
```

**失败响应示例**:

```json
// 409 Conflict - 用户ID已存在
{
  "error": "用户 ID 已存在"
}

// 409 Conflict - MAC地址已存在
{
  "error": "MAC地址已存在"
}

// 400 Bad Request - 参数验证失败
{
  "error": "用户名必须是4-20个字符"
}
```

#### 6.2.2 用户列表查询

**HTTP 方法**: GET

**路径**: `/search/users`

**功能**: 获取系统中所有用户的列表信息

**请求参数**:

| 参数名    | 类型    | 必填 | 默认值 | 描述     |
| --------- | ------- | ---- | ------ | -------- |
| page      | Integer | 否   | 1      | 页码     |
| page_size | Integer | 否   | 10     | 每页大小 |

**cURL 请求示例**:

```bash
curl -X GET "http://localhost:8080/search/users?page=1&page_size=10"
```

**成功响应 (200 OK)**:

```json
{
  "code": 200,
  "message": "获取用户列表成功",
  "data": [
    {
      "user_name": "testuser",
      "user_id": "U0000001",
      "user_type": 1,
      "gateway_device_id": "G00000001",
      "email": "<EMAIL>",
      "auth_status": "normal",
      "login_status": "offline",
      "user_location": "guangzhou",
      "mac_address": "AA:BB:CC:DD:EE:FF",
      "is_superior_forbidden": false,
      "created_at": "2025-06-18T10:30:00Z"
    }
  ]
}
```

#### 6.2.3 单个用户查询

**HTTP 方法**: GET

**路径**: `/search/user`

**功能**: 根据用户ID查询单个用户的详细信息

**请求参数**:

| 参数名 | 类型   | 必填 | 描述         |
| ------ | ------ | ---- | ------------ |
| id     | String | 是   | 用户唯一标识 |

**cURL 请求示例**:

```bash
curl -X GET "http://localhost:8080/search/user?id=U0000001"
```

**成功响应 (200 OK)**:

```json
{
  "code": 200,
  "message": "用户查询成功",
  "data": {
    "user_name": "testuser",
    "user_id": "U0000001",
    "user_type": 1,
    "gateway_device_id": "G00000001",
    "email": "<EMAIL>",
    "auth_status": "normal",
    "login_status": "offline",
    "user_location": "guangzhou",
    "mac_address": "AA:BB:CC:DD:EE:FF",
    "is_superior_forbidden": false,
    "created_at": "2025-06-18T10:30:00Z"
  }
}
```

**响应体字段说明**:

| 字段                  | 类型    | 描述                                                         |
| --------------------- | ------- | ------------------------------------------------------------ |
| user_name             | String  | 用户名                                                       |
| user_id               | String  | 用户唯一标识                                                 |
| user_type             | Integer | 用户类型                                                     |
| gateway_device_id     | String  | 用户所属网关设备ID                                           |
| email                 | String  | 用户邮箱                                                     |
| auth_status           | String  | 认证状态                                                     |
| login_status          | String  | 登录状态                                                     |
| user_location         | String  | 用户位置信息                                                 |
| mac_address           | String  | 用户设备MAC地址（始终返回，空值时为空字符串）                |
| is_superior_forbidden | Boolean | 上级设备冻结状态（动态计算字段）                             |
| created_at            | String  | 创建时间（ISO 8601格式）                                     |
| updated_at            | String  | 更新时间（ISO 8601格式，可选）                               |

**动态计算字段说明**:
- `is_superior_forbidden`:
  - `true`: 用户的网关设备或其上级设备链路中存在处于冻结状态（auth_status="forbidden"）的设备
  - `false`: 用户的整个上级设备链路中没有冻结状态的设备

**失败响应示例**:

```json
// 400 Bad Request - 缺少参数
{
  "error": "缺少必要的id参数"
}

// 404 Not Found - 用户不存在
{
  "error": "用户不存在"
}
```

#### 6.2.4 用户信息更新

**HTTP 方法**: PUT

**路径**: `/update/user`

**功能**: 更新用户信息

**查询参数**:

| 参数名 | 类型   | 必填 | 描述         |
| ------ | ------ | ---- | ------------ |
| id     | String | 是   | 用户唯一标识 |

**请求体参数**（JSON格式，所有字段均为可选）:

| 字段名            | 类型    | 必填 | 描述                                                    |
| ----------------- | ------- | ---- | ------------------------------------------------------- |
| user_name         | String  | 否   | 用户名，4-20字符                                        |
| pass_wd           | String  | 否   | 密码，最少8字符                                         |
| user_id           | String  | 否   | 新的用户唯一标识                                        |
| user_type         | Integer | 否   | 用户类型                                                |
| gateway_device_id | String  | 否   | 用户所属网关设备ID                                      |
| auth_status       | String  | 否   | 认证状态（"normal", "forbidden", "signout"）            |
| login_status      | String  | 否   | 登录状态（"online", "offline"）                         |
| cert_id           | String  | 否   | 证书ID                                                  |
| key_id            | String  | 否   | 密钥ID                                                  |
| email             | String  | 否   | 邮箱                                                    |
| user_location     | String  | 否   | 用户位置信息                                            |
| mac_address       | String  | 否   | 用户设备MAC地址，格式：XX:XX:XX:XX:XX:XX 或 XX-XX-XX-XX-XX-XX |

**cURL 请求示例**:

```bash
curl -X PUT "http://localhost:8080/update/user?id=U0000001" \
  -H "Content-Type: application/json" \
  -d '{
    "user_location": "beijing",
    "auth_status": "normal"
  }'
```

**成功响应 (200 OK)**:

```json
{
  "code": 200,
  "message": "用户信息更新成功",
  "data": {
    "user_name": "testuser",
    "user_id": "U0000001",
    "user_type": 1,
    "gateway_device_id": "G00000001",
    "email": "<EMAIL>",
    "auth_status": "normal",
    "login_status": "offline",
    "user_location": "beijing",
    "mac_address": "AA:BB:CC:DD:EE:FF",
    "is_superior_forbidden": false,
    "created_at": "2025-06-18T10:30:00Z",
    "updated_at": "2025-06-18T11:00:00Z"
  }
}
```

**MAC地址更新说明**：
- 支持标准MAC地址格式验证
- 自动检查MAC地址唯一性（不能与其他用户重复）
- 可以设置为空字符串来清除MAC地址

**失败响应示例**:

```json
// 400 Bad Request - 参数验证失败
{
  "error": "用户名必须是4-20个字符"
}

// 409 Conflict - MAC地址冲突
{
  "error": "MAC地址已被其他用户使用"
}

// 404 Not Found - 用户不存在
{
  "error": "用户不存在"
}
```

### 6.3 设备管理接口

#### 6.3.1 设备注册

**HTTP 方法**: POST

**路径**: `/regist/devices`

**功能**: 注册新设备到系统中

**请求参数**:

| 字段名              | 类型    | 必填 | 描述                                                    |
| ------------------- | ------- | ---- | ------------------------------------------------------- |
| device_name         | String  | 是   | 设备名称，4-50字符                                      |
| device_type         | Integer | 是   | 设备类型（1-3:网关设备，4:安全接入管理设备）            |
| pass_wd             | String  | 是   | 设备密码，最少8字符                                     |
| device_id           | String  | 是   | 设备唯一标识                                            |
| superior_device_id  | String  | 是   | 上级设备ID（安全接入管理设备为"0"）                     |
| register_ip         | String  | 是   | 设备注册目标IP地址，表示设备要连接的目标服务器IP地址    |
| hardware_fingerprint| String   | 否   | 设备硬件指纹，用于设备唯一性识别                        |
| long_address        | String  | 否   | 网关设备通讯长地址（可选）                              |
| short_address       | String  | 否   | 网关设备通讯短地址（可选）                              |
| ses_key             | String  | 否   | 网关设备SES密钥（可选）                                 |
| auth_status         | String  | 否   | 认证状态，默认"normal"                                  |
| login_status        | String  | 否   | 登录状态，默认"offline"                                 |
| gateway_location    | String  | 否   | 网关位置信息                                            |

**cURL 请求示例**:

```bash
curl -X POST "http://localhost:8080/regist/devices" \
  -H "Content-Type: application/json" \
  -d '{
    "device_name": "GW00000001",
    "device_type": 1,
    "pass_wd": "password123",
    "device_id": "G00000001",
    "superior_device_id": "S00000001",
    "register_ip": "*************",
    "hardware_fingerprint": "ABC123DEF456789012345678901234567890",
    "long_address": "2001:db8::1",
    "short_address": "0x1234",
    "ses_key": "ses_key_value",
    "gateway_location": "guangzhou"
  }'
```

**成功响应 (200 OK)**:

```json
{
  "code": 200,
  "message": "设备注册成功",
  "data": {
    "device_name": "GW00000001",
    "device_type": 1,
    "device_id": "G00000001",
    "superior_device_id": "S00000001",
    "register_ip": "*************",
    "hardware_fingerprint": "ABC123DEF456789012345678901234567890",
    "auth_status": "normal",
    "login_status": "offline",
    "gateway_location": "guangzhou",
    "created_at": "2025-06-18T10:30:00Z"
  }
}
```

**失败响应示例**:

```json
// 409 Conflict - 设备ID已存在
{
  "error": "设备 ID 已存在"
}

// 400 Bad Request - 上级设备不存在
{
  "error": "指定的上级设备不存在"
}
```

#### 6.3.2 设备列表查询

**HTTP 方法**: GET

**路径**: `/search/devices`

**功能**: 获取系统中所有设备的列表信息

**请求参数**:

| 参数名    | 类型    | 必填 | 默认值 | 描述     |
| --------- | ------- | ---- | ------ | -------- |
| page      | Integer | 否   | 1      | 页码     |
| page_size | Integer | 否   | 10     | 每页大小 |

**cURL 请求示例**:

```bash
curl -X GET "http://localhost:8080/search/devices?page=1&page_size=10"
```

**成功响应 (200 OK)**:

```json
{
  "code": 200,
  "message": "获取设备列表成功",
  "data": [
    {
      "device_name": "GW00000001",
      "device_type": 1,
      "device_id": "G00000001",
      "superior_device_id": "S00000001",
      "register_ip": "*************",
      "auth_status": "normal",
      "login_status": "offline",
      "is_ready": true,
      "gateway_location": "guangzhou",
      "is_superior_forbidden": false,
      "created_at": "2025-06-18T10:30:00Z"
    }
  ]
}
```

#### 6.3.3 单个设备查询

**HTTP 方法**: GET

**路径**: `/search/device`

**功能**: 根据设备ID查询单个设备的详细信息

**请求参数**:

| 参数名 | 类型   | 必填 | 描述         |
| ------ | ------ | ---- | ------------ |
| id     | String | 是   | 设备唯一标识 |

**cURL 请求示例**:

```bash
curl -X GET "http://localhost:8080/search/device?id=G00000001"
```

**成功响应 (200 OK)**:

```json
{
  "code": 200,
  "message": "设备查询成功",
  "data": {
    "device_name": "GW00000001",
    "device_type": 1,
    "device_id": "G00000001",
    "superior_device_id": "S00000001",
    "register_ip": "*************",
    "email": "",
    "cert_id": "",
    "key_id": "",
    "auth_status": "normal",
    "login_status": "offline",
    "is_ready": true,
    "gateway_location": "guangzhou",
    "online_duration": 0,
    "is_superior_forbidden": false,
    "created_at": "2025-06-18T10:30:00Z"
  }
}
```

**响应体字段说明**:

| 字段                  | 类型    | 描述                                                         |
| --------------------- | ------- | ------------------------------------------------------------ |
| device_name           | String  | 设备名称                                                     |
| device_type           | Integer | 设备类型（1-3:网关设备，4:安全接入管理设备）                |
| device_id             | String  | 设备唯一标识                                                 |
| superior_device_id    | String  | 上级设备ID                                                   |
| register_ip           | String  | 设备注册目标IP地址（设备要连接的目标服务器IP地址）           |
| email                 | String  | 关联邮箱                                                     |
| cert_id               | String  | 关联的证书ID                                                 |
| key_id                | String  | 关联的密钥ID                                                 |
| auth_status           | String  | 认证状态                                                     |
| login_status          | String  | 登录状态                                                     |
| is_ready              | Boolean | 设备就绪状态                                                 |
| gateway_location      | String  | 网关位置信息                                                 |
| online_duration       | Integer | 在线时长                                                     |
| is_superior_forbidden | Boolean | 上级设备冻结状态（动态计算字段）                             |
| created_at            | String  | 创建时间（ISO 8601格式）                                     |
| updated_at            | String  | 更新时间（ISO 8601格式，可选）                               |

**动态计算字段说明**:
- `is_superior_forbidden`:
  - `true`: 设备的上级设备链路中存在处于冻结状态（auth_status="forbidden"）的设备
  - `false`: 设备的整个上级设备链路中没有冻结状态的设备
  - 注意：安全接入管理设备（类型4）没有上级设备，此字段始终为false

**安全字段说明**：
- 出于安全考虑，设备密码、SES密钥等敏感信息不会在查询响应中返回

#### 6.3.4 设备信息更新

**HTTP 方法**: PUT

**路径**: `/update/device`

**功能**: 更新设备信息

**查询参数**:

| 参数名 | 类型   | 必填 | 描述         |
| ------ | ------ | ---- | ------------ |
| id     | String | 是   | 设备唯一标识 |

**请求体参数**（JSON格式，所有字段均为可选）:

| 字段名             | 类型    | 必填 | 描述                                        |
| ------------------ | ------- | ---- | ------------------------------------------- |
| device_name        | String  | 否   | 设备名称                                    |
| device_type        | Integer | 否   | 设备类型                                    |
| pass_wd            | String  | 否   | 设备密码                                    |
| superior_device_id | String  | 否   | 上级设备ID                                  |
| auth_status        | String  | 否   | 认证状态（"normal", "forbidden", "signout"）|
| login_status       | String  | 否   | 登录状态（"online", "offline"）             |
| cert_id            | String  | 否   | 证书ID                                      |
| key_id             | String  | 否   | 密钥ID                                      |
| register_ip        | String  | 否   | 设备注册目标IP地址                          |
| email              | String  | 否   | 邮箱                                        |
| long_address       | String  | 否   | 长地址                                      |
| short_address      | String  | 否   | 短地址                                      |
| ses_key            | String  | 否   | SES密钥                                     |
| gateway_location   | String  | 否   | 网关位置信息                                |

**cURL 请求示例**:

```bash
curl -X PUT "http://localhost:8080/update/device?id=G00000001" \
  -H "Content-Type: application/json" \
  -d '{
    "gateway_location": "beijing",
    "auth_status": "normal"
  }'
```

**成功响应 (200 OK)**:

```json
{
  "code": 200,
  "message": "设备信息更新成功",
  "data": {
    "device_name": "GW00000001",
    "device_type": 1,
    "device_id": "G00000001",
    "superior_device_id": "S00000001",
    "register_ip": "*************",
    "auth_status": "normal",
    "login_status": "offline",
    "is_ready": true,
    "gateway_location": "beijing",
    "is_superior_forbidden": false,
    "created_at": "2025-06-18T10:30:00Z",
    "updated_at": "2025-06-18T11:00:00Z"
  }
}
```

#### 6.3.5 设备就绪状态列表查询

**HTTP 方法**: GET

**路径**: `/search/devices/is_ready`

**功能**: 查询所有设备的就绪状态信息，支持分页和条件筛选

**请求参数**:

| 参数名      | 类型    | 必填 | 默认值 | 取值范围   | 描述                                             |
| ----------- | ------- | ---- | ------ | ---------- | ------------------------------------------------ |
| device_type | Integer | 否   | -      | 1-4        | 设备类型筛选（1-3:网关设备，4:安全接入管理设备） |
| is_ready    | Boolean | 否   | -      | true/false | 就绪状态筛选                                     |
| page        | Integer | 否   | 1      | ≥1        | 页码                                             |
| page_size   | Integer | 否   | 10     | 1-100      | 每页大小                                         |

**cURL 请求示例**:

```bash
# 基本查询
curl -X GET "http://localhost:8080/search/devices/is_ready?page=1&page_size=10"

# 按设备类型筛选
curl -X GET "http://localhost:8080/search/devices/is_ready?device_type=1&page=1&page_size=5"

# 按就绪状态筛选
curl -X GET "http://localhost:8080/search/devices/is_ready?is_ready=false&page_size=20"

# 组合筛选
curl -X GET "http://localhost:8080/search/devices/is_ready?device_type=4&is_ready=true&page=1&page_size=15"
```

**成功响应 (200 OK)**:

```json
{
  "code": 200,
  "message": "设备就绪状态查询成功",
  "data": {
    "devices": [
      {
        "device_id": "000000000001",
        "device_name": "EDGE00000001",
        "device_type": 4,
        "is_ready": false,
        "auth_status": "normal",
        "login_status": "offline",
        "created_at": "2025-06-13T00:15:49Z",
        "updated_at": "2025-06-13T00:15:49Z"
      },
      {
        "device_id": "ID1",
        "device_name": "GW00000000001",
        "device_type": 1,
        "is_ready": false,
        "auth_status": "normal",
        "login_status": "online",
        "created_at": "2025-05-27T01:07:28Z",
        "updated_at": "2025-06-14T16:35:02Z"
      }
    ],
    "total": 22,
    "page": 1,
    "page_size": 10,
    "ready_count": 0,
    "not_ready_count": 22
  }
}
```

**响应体字段说明**:

| 字段                        | 类型    | 描述                                         |
| --------------------------- | ------- | -------------------------------------------- |
| code                        | Integer | 响应状态码                                   |
| message                     | String  | 响应消息                                     |
| data                        | Object  | 响应数据                                     |
| data.devices                | Array   | 设备就绪状态信息数组                         |
| data.devices[].device_id    | String  | 设备唯一标识                                 |
| data.devices[].device_name  | String  | 设备名称                                     |
| data.devices[].device_type  | Integer | 设备类型（1-3:网关设备，4:安全接入管理设备） |
| data.devices[].is_ready     | Boolean | 设备就绪状态                                 |
| data.devices[].auth_status  | String  | 认证状态                                     |
| data.devices[].login_status | String  | 登录状态                                     |
| data.devices[].created_at   | String  | 创建时间（ISO 8601格式）                     |
| data.devices[].updated_at   | String  | 更新时间（ISO 8601格式，可选）               |
| data.total                  | Integer | 符合条件的设备总数                           |
| data.page                   | Integer | 当前页码                                     |
| data.page_size              | Integer | 每页大小                                     |
| data.ready_count            | Integer | 就绪设备数量（基于筛选条件）                 |
| data.not_ready_count        | Integer | 未就绪设备数量（基于筛选条件）               |

**失败响应示例**:

```json
// 400 Bad Request - 参数错误
{
  "error": "设备类型必须是1-4之间的整数"
}

// 400 Bad Request - 页面大小超限
{
  "error": "页面大小必须是1-100之间的正整数"
}

// 400 Bad Request - 就绪状态参数错误
{
  "error": "就绪状态必须是true或false"
}

// 500 Internal Server Error - 服务器错误
{
  "error": "数据库连接失败"
}
```

#### 6.3.6 单个设备就绪状态查询

**HTTP 方法**: GET

**路径**: `/search/device/is_ready`

**功能**: 根据设备ID查询单个设备的就绪状态信息

**请求参数**:

| 参数名 | 类型   | 必填 | 描述         |
| ------ | ------ | ---- | ------------ |
| id     | String | 是   | 设备唯一标识 |

**cURL 请求示例**:

```bash
curl -X GET "http://localhost:8080/search/device/is_ready?id=000000000001"
```

**成功响应 (200 OK)**:

```json
{
  "code": 200,
  "message": "设备就绪状态查询成功",
  "data": {
    "device_id": "000000000001",
    "device_name": "EDGE00000001",
    "device_type": 4,
    "is_ready": false,
    "auth_status": "normal",
    "login_status": "offline",
    "created_at": "2025-06-13T00:15:49Z",
    "updated_at": "2025-06-13T00:15:49Z"
  }
}
```

**响应体字段说明**:

| 字段              | 类型    | 描述                                         |
| ----------------- | ------- | -------------------------------------------- |
| code              | Integer | 响应状态码                                   |
| message           | String  | 响应消息                                     |
| data              | Object  | 设备就绪状态信息                             |
| data.device_id    | String  | 设备唯一标识                                 |
| data.device_name  | String  | 设备名称                                     |
| data.device_type  | Integer | 设备类型（1-3:网关设备，4:安全接入管理设备） |
| data.is_ready     | Boolean | 设备就绪状态                                 |
| data.auth_status  | String  | 认证状态                                     |
| data.login_status | String  | 登录状态                                     |
| data.created_at   | String  | 创建时间（ISO 8601格式）                     |
| data.updated_at   | String  | 更新时间（ISO 8601格式，可选）               |

**失败响应示例**:

```json
// 400 Bad Request - 缺少必需参数
{
  "error": "缺少必要的id参数"
}

// 404 Not Found - 设备不存在
{
  "error": "设备不存在"
}

// 500 Internal Server Error - 服务器错误
{
  "error": "数据库连接失败"
}
```

### 6.4 证书管理接口

#### 6.4.1 用户证书绑定

**HTTP 方法**: POST

**路径**: `/bind/users/{id}/cert`

**功能**: 为指定用户绑定证书文件

**路径参数**:

| 参数名 | 类型   | 必填 | 描述         |
| ------ | ------ | ---- | ------------ |
| id     | String | 是   | 用户唯一标识 |

**请求体参数**（表单数据）:

| 参数名 | 类型 | 必填 | 描述                    |
| ------ | ---- | ---- | ----------------------- |
| cert   | File | 是   | 证书文件（.pem格式，最大8MB） |

**cURL 请求示例**:

```bash
curl -X POST "http://localhost:8080/bind/users/U0000001/cert" \
  -F "cert=@user_cert.pem"
```

**成功响应 (200 OK)**:

```json
{
  "code": 200,
  "message": "证书绑定成功",
  "data": {
    "userID": "U0000001",
    "certPath": "regist/certs/certs/user_U0000001_cert.pem"
  }
}
```

**失败响应示例**:

```json
// 400 Bad Request - 文件格式错误
{
  "error": "文件必须是.pem格式"
}

// 400 Bad Request - 文件大小超限
{
  "error": "文件大小超过限制"
}

// 404 Not Found - 用户不存在
{
  "error": "用户不存在"
}
```

#### 6.4.2 用户密钥绑定

**HTTP 方法**: POST

**路径**: `/bind/users/{id}/key`

**功能**: 为指定用户绑定密钥文件

**路径参数**:

| 参数名 | 类型   | 必填 | 描述         |
| ------ | ------ | ---- | ------------ |
| id     | String | 是   | 用户唯一标识 |

**请求体参数**（表单数据）:

| 参数名 | 类型 | 必填 | 描述                    |
| ------ | ---- | ---- | ----------------------- |
| key    | File | 是   | 密钥文件（.pem格式，最大8MB） |

**cURL 请求示例**:

```bash
curl -X POST "http://localhost:8080/bind/users/U0000001/key" \
  -F "key=@user_key.pem"
```

**成功响应 (200 OK)**:

```json
{
  "code": 200,
  "message": "密钥绑定成功",
  "data": {
    "userID": "U0000001",
    "keyPath": "regist/certs/keys/user_U0000001_key.pem"
  }
}
```

#### 6.4.3 设备证书绑定

**HTTP 方法**: POST

**路径**: `/bind/devices/{id}/cert`

**功能**: 为指定设备绑定证书文件

**路径参数**:

| 参数名 | 类型   | 必填 | 描述         |
| ------ | ------ | ---- | ------------ |
| id     | String | 是   | 设备唯一标识 |

**请求体参数**（表单数据）:

| 参数名 | 类型 | 必填 | 描述                    |
| ------ | ---- | ---- | ----------------------- |
| cert   | File | 是   | 证书文件（.pem格式，最大8MB） |

**cURL 请求示例**:

```bash
curl -X POST "http://localhost:8080/bind/devices/G00000001/cert" \
  -F "cert=@device_cert.pem"
```

**成功响应 (200 OK)**:

```json
{
  "code": 200,
  "message": "证书绑定成功",
  "data": {
    "deviceID": "G00000001",
    "certPath": "regist/certs/certs/device_G00000001_cert.pem"
  }
}
```

#### 6.4.4 设备密钥绑定

**HTTP 方法**: POST

**路径**: `/bind/devices/{id}/key`

**功能**: 为指定设备绑定密钥文件

**路径参数**:

| 参数名 | 类型   | 必填 | 描述         |
| ------ | ------ | ---- | ------------ |
| id     | String | 是   | 设备唯一标识 |

**请求体参数**（表单数据）:

| 参数名 | 类型 | 必填 | 描述                    |
| ------ | ---- | ---- | ----------------------- |
| key    | File | 是   | 密钥文件（.pem格式，最大8MB） |

**cURL 请求示例**:

```bash
curl -X POST "http://localhost:8080/bind/devices/G00000001/key" \
  -F "key=@device_key.pem"
```

**成功响应 (200 OK)**:

```json
{
  "code": 200,
  "message": "密钥绑定成功",
  "data": {
    "deviceID": "G00000001",
    "keyPath": "regist/certs/keys/device_G00000001_key.pem"
  }
}
```

#### 6.4.5 证书信息查询

**HTTP 方法**: GET

**路径**: `/cert/info`

**功能**: 查询证书信息

**请求参数**:

| 参数名      | 类型   | 必填 | 描述                        |
| ----------- | ------ | ---- | --------------------------- |
| type        | String | 否   | 实体类型（"user"或"device"）|
| id          | String | 否   | 实体ID                      |
| cert_id     | String | 否   | 证书ID                      |

**cURL 请求示例**:

```bash
curl -X GET "http://localhost:8080/cert/info?type=user&id=U0000001"
```

**成功响应 (200 OK)**:

```json
{
  "code": 200,
  "message": "证书信息查询成功",
  "data": {
    "entity_type": "user",
    "entity_id": "U0000001",
    "cert_path": "regist/certs/certs/user_U0000001_cert.pem",
    "key_path": "regist/certs/keys/user_U0000001_key.pem",
    "upload_time": "2025-06-18T10:30:00Z"
  }
}
```

**失败响应示例**:

```json
// 400 Bad Request - 参数错误
{
  "error": "缺少必要的参数"
}

// 400 Bad Request - 实体类型错误
{
  "error": "无效的实体类型，必须是user或device"
}

// 404 Not Found - 证书不存在
{
  "error": "证书信息不存在"
}
```

### 6.4 搜索接口（待实现）

> **注意**: 以下搜索接口为规划中的功能，当前版本尚未实现。如需使用搜索功能，请使用现有的用户列表查询和设备列表查询接口。

#### 6.4.1 综合搜索（规划中）

**HTTP 方法**: GET

**路径**: `/search/all`

**功能**: 在用户和设备中进行综合搜索

**状态**: 🚧 待实现

**请求参数**:

| 参数名    | 类型    | 必填 | 默认值 | 描述                        |
| --------- | ------- | ---- | ------ | --------------------------- |
| query     | String  | 是   | -      | 搜索关键词                  |
| type      | String  | 否   | -      | 搜索类型（"user"或"device"）|
| page      | Integer | 否   | 1      | 页码                        |
| page_size | Integer | 否   | 10     | 每页大小                    |

#### 6.4.2 用户名搜索（规划中）

**HTTP 方法**: GET

**路径**: `/search/users/by_name`

**功能**: 根据用户名关键词搜索用户

**状态**: 🚧 待实现

**请求参数**:

| 参数名    | 类型    | 必填 | 默认值 | 描述         |
| --------- | ------- | ---- | ------ | ------------ |
| name      | String  | 是   | -      | 用户名关键词 |
| page      | Integer | 否   | 1      | 页码         |
| page_size | Integer | 否   | 10     | 每页大小     |

#### 6.4.3 设备名搜索（规划中）

**HTTP 方法**: GET

**路径**: `/search/devices/by_name`

**功能**: 根据设备名关键词搜索设备

**状态**: 🚧 待实现

**请求参数**:

| 参数名    | 类型    | 必填 | 默认值 | 描述         |
| --------- | ------- | ---- | ------ | ------------ |
| name      | String  | 是   | -      | 设备名关键词 |
| page      | Integer | 否   | 1      | 页码         |
| page_size | Integer | 否   | 10     | 每页大小     |

**替代方案**:

在搜索接口实现之前，可以使用以下现有接口：

- **用户查询**: 使用 `GET /search/users` 获取所有用户列表，然后在前端进行过滤
- **设备查询**: 使用 `GET /search/devices` 获取所有设备列表，然后在前端进行过滤
- **单个查询**: 使用 `GET /search/user?id={userID}` 或 `GET /search/device?id={deviceID}` 进行精确查询

### 6.5 事件记录查询接口

查询用户/设备的操作事件记录，支持多种类型的事件查询。

**HTTP 方法**: GET

**路径**: `/api/get/events`

**请求参数**:

| 字段        | 类型   | 是否必需 | 描述                                             |
| ----------- | ------ | -------- | ------------------------------------------------ |
| start_time  | String | 是       | 起始时间，格式：YYYY-MM-DD HH:mm:ss              |
| end_time    | String | 是       | 结束时间，格式：YYYY-MM-DD HH:mm:ss              |
| event_code  | String | 否       | 事件代码过滤，4位数字字符串                      |
| entity_type | String | 否       | 实体类型过滤：01(用户)/02(网关)/03(边缘控制软件) |

**事件代码说明**:

**前两位 - 事件类型**：

- `01`: 上线
- `02`: 下线
- `03`: 认证失败
- `04`: 发送数据
- `05`: 用户操作
- `06`: 接收数据
- `07`: 异常

**后两位 - 主体身份**：

- `01`: 用户
- `02`: 网关
- `03`: 边缘控制软件

**请求示例 (cURL)**:

```bash
# 基本查询
curl -X GET "http://localhost:8080/api/get/events?start_time=2025-01-01%2000:00:00&end_time=2025-12-31%2023:59:59"

# 按事件代码查询（用户上线事件）
curl -X GET "http://localhost:8080/api/get/events?start_time=2025-01-01%2000:00:00&end_time=2025-12-31%2023:59:59&event_code=0101"

# 按实体类型查询（用户事件）
curl -X GET "http://localhost:8080/api/get/events?start_time=2025-01-01%2000:00:00&end_time=2025-12-31%2023:59:59&entity_type=01"
```

**成功响应 (200 OK)**:

```json
[
  {
    "event_code": "0101",
    "entity_id": "U00000001",
    "entity_type": "01",
    "superior_device_id": "G00000001",
    "location": "guangzhou",
    "operation_time": "2025-01-15T08:30:00+08:00"
  },
  {
    "event_code": "0102",
    "entity_id": "G00000001",
    "entity_type": "02",
    "superior_device_id": "S00000001",
    "location": "guangzhou",
    "operation_time": "2025-01-15T08:00:00+08:00"
  }
]
```

**响应体字段说明**:

| 字段               | 类型   | 描述                                              |
| ------------------ | ------ | ------------------------------------------------- |
| event_code         | String | 事件代码，4位数字字符串                           |
| entity_id          | String | 实体ID（用户ID或设备ID）                          |
| entity_type        | String | 实体类型编码（01=用户，02=网关，03=边缘控制软件） |
| superior_device_id | String | 上级设备ID，如果是安全接入管理设备则为null        |
| location           | String | 位置信息                                          |
| operation_time     | String | 操作时间，ISO 8601格式                            |

**失败响应 (400 Bad Request)**:

```json
{
  "code": 400,
  "message": "缺少必要的时间参数",
  "data": null
}
```

```json
{
  "code": 400,
  "message": "事件代码必须为4位数字字符串",
  "data": null
}
```

**失败响应 (500 Internal Server Error)**:

```json
{
  "code": 500,
  "message": "数据库连接失败",
  "data": null
}
```

### 6.6 登录记录查询接口（兼容性）

为保持向后兼容性而保留的接口，功能与事件记录查询接口相同。

**HTTP 方法**: GET

**路径**: `/api/get/login`

**说明**: 此接口已废弃，建议使用 `/api/get/events` 接口。请求参数和响应格式与事件记录查询接口完全相同。

## 7. 特性与优势

1. **完整的注册管理**：提供用户和设备的全生命周期管理功能
2. **灵活的证书绑定**：支持用户和设备的证书与密钥绑定机制
3. **数据验证机制**：全面的输入验证确保数据完整性和有效性
4. **配置联动**：与配置生成模块联动，自动触发相关配置更新
5. **详细的日志记录**：通过中间件记录请求和响应详情，便于调试和审计
6. **安全控制**：文件大小限制和格式验证保障系统安全
7. **CORS支持**：内置跨域资源共享支持，便于前端调用
8. **位置信息支持**：支持记录和管理用户、网关设备和软件设备的位置信息
9. **设备分类管理**：按设备类型分别管理网关设备(1-3)和软件设备(4)的位置信息
10. **多类型事件追踪**：通过操作事件表记录用户和设备的多种操作活动历史
11. **地理分布可视化**：提供位置查询接口支持前端地理分布展示，支持三类设备分别展示
12. **事件记录查询**：支持按时间范围、事件类型、实体类型等多维度查询操作事件记录
13. **事件代码体系**：采用4位数字编码系统，支持7种事件类型和3种主体身份的组合
14. **实体类型编码化**：使用编码存储实体类型，提高存储效率和查询性能
15. **向后兼容性**：保留原有登录记录查询接口，确保平滑迁移
16. **设备就绪状态监控**：提供专门的设备就绪状态查询接口，支持实时监控设备状态
17. **灵活的状态筛选**：支持按设备类型、就绪状态等多维度筛选设备
18. **状态统计功能**：自动统计就绪和未就绪设备数量，便于运维监控
19. **高性能状态查询**：通过索引优化和分页机制，确保大规模设备环境下的查询性能
20. **🔒 API安全增强**：移除所有面向前端API响应中的敏感密码字段，提升系统安全性

## 8. API安全改进

### 8.1 敏感字段移除

**更新时间**: 2025年1月20日
**版本**: v2.0.0

为了提升系统安全性，注册管理模块已完成重要的安全改进工作，移除了所有面向前端API响应中的敏感密码字段。

#### 8.1.1 改进内容

**用户管理接口安全改进**：
- ✅ 移除用户查询接口响应中的 `pass_wd` 字段
- ✅ 移除用户注册接口响应中的 `pass_wd` 字段
- ✅ 移除用户更新接口响应中的 `pass_wd` 字段
- ✅ 保持用户认证功能正常工作

**设备管理接口安全改进**：
- ✅ 移除设备查询接口响应中的 `ses_key` 字段
- ✅ 移除设备注册接口响应中的 `ses_key` 字段
- ✅ 移除设备更新接口响应中的 `ses_key` 字段
- ✅ 保持设备配置生成功能正常工作

#### 8.1.2 影响的接口

**用户管理接口**（移除 `pass_wd` 字段）：
- `GET /search/users` - 用户列表查询
- `GET /search/user` - 单用户查询
- `POST /regist/users` - 用户注册响应
- `PUT /update/user` - 用户更新响应

**设备管理接口**（移除 `ses_key` 字段）：
- `GET /search/devices` - 设备列表查询
- `GET /search/device` - 单设备查询
- `POST /regist/devices` - 设备注册响应
- `PUT /update/device` - 设备更新响应

#### 8.1.3 例外接口（保持不变）

以下接口因业务需要保持原有功能：
- **配置文件生成接口**: 用于系统内部配置，需要完整的认证信息
- **配置文件下发接口**: 用于设备配置同步
- **用户/设备认证接口**: 登录验证等必需场景

#### 8.1.4 响应格式变更示例

**用户查询接口响应变更**：

修改前（包含敏感信息）：
```json
{
  "code": 200,
  "message": "获取用户列表成功",
  "data": [
    {
      "user_name": "testuser",
      "user_id": "000000001000",
      "pass_wd": "hashedpassword123",  // ❌ 敏感字段已移除
      "user_type": 1,
      "gateway_device_id": "000000000001",
      "auth_status": "normal",
      "login_status": "offline"
    }
  ]
}
```

修改后（安全）：
```json
{
  "code": 200,
  "message": "获取用户列表成功",
  "data": [
    {
      "user_name": "testuser",
      "user_id": "000000001000",
      // pass_wd字段已移除 ✅
      "user_type": 1,
      "gateway_device_id": "000000000001",
      "auth_status": "normal",
      "login_status": "offline",
      "is_superior_forbidden": false  // 新增：上级设备冻结状态
    }
  ]
}
```

**设备查询接口响应变更**：

修改前（包含敏感信息）：
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "device_name": "gateway001",
      "device_id": "000000000001",
      "device_type": 1,
      "ses_key": "sensitive_ses_key_value",  // ❌ 敏感字段已移除
      "long_address": "2001:db8::1",
      "short_address": "0x1234",
      "auth_status": "normal",
      "login_status": "online"
    }
  ]
}
```

修改后（安全）：
```json
{
  "code": 200,
  "message": "查询成功",
  "data": [
    {
      "device_name": "gateway001",
      "device_id": "000000000001",
      "device_type": 1,
      // ses_key字段已移除 ✅
      "long_address": "2001:db8::1",
      "short_address": "0x1234",
      "auth_status": "normal",
      "login_status": "online",
      "is_superior_forbidden": false  // 新增：上级设备冻结状态
    }
  ]
}
```

#### 8.1.5 技术实现

**代码层面修改**：
- 修改 `regist/handler/response.go` 中的响应结构体
- 移除 `UserResponse.Password` 字段
- 移除 `DeviceResponse.SESKey` 字段
- 更新相应的转换函数

**数据库层面**：
- 数据库模型保持不变，仅修改API响应层
- 配置生成功能继续使用完整的数据库信息

#### 8.1.6 安全收益

- ✅ **消除密码泄露风险**: 前端无法获取用户密码信息
- ✅ **消除SES密钥泄露风险**: 前端无法获取设备SES密钥
- ✅ **符合安全最佳实践**: 遵循最小权限原则
- ✅ **保持业务功能完整**: 所有业务功能正常工作
- ✅ **向后兼容**: 除敏感字段外，其他字段保持不变

## 9. 使用示例

### 9.1 注册用户

```go
// 创建用户JSON请求体
userJson := `{
    "user_name": "testuser",
    "pass_wd": "password123",
    "user_id": "user_123456",
    "user_type": 1,
    "gateway_device_id": "device_001",
    "email": "<EMAIL>"
}`

// 发送POST请求注册用户
resp, err := http.Post("http://localhost:8080/regist/users",
    "application/json",
    strings.NewReader(userJson))
if err != nil {
    log.Fatalf("注册用户请求失败: %v", err)
}
defer resp.Body.Close()

// 解析响应（注意：响应中不包含密码字段）
body, _ := io.ReadAll(resp.Body)
if resp.StatusCode == http.StatusOK {
    log.Printf("用户注册成功：%s", string(body))
    // 响应示例：
    // {
    //   "code": 200,
    //   "message": "用户注册成功",
    //   "data": {
    //     "user_name": "testuser",
    //     "user_id": "user_123456",
    //     // pass_wd字段已移除，不在响应中返回
    //     "user_type": 1,
    //     "gateway_device_id": "device_001",
    //     "email": "<EMAIL>",
    //     "auth_status": "normal",
    //     "login_status": "offline"
    //   }
    // }
} else {
    log.Printf("用户注册失败：%s", string(body))
}
```

### 9.2 查询用户信息

```go
// 发送GET请求查询用户
resp, err := http.Get("http://localhost:8080/search/user?id=user_123456")
if err != nil {
    log.Fatalf("查询用户请求失败: %v", err)
}
defer resp.Body.Close()

// 解析响应（注意：响应中不包含密码字段）
body, _ := io.ReadAll(resp.Body)
if resp.StatusCode == http.StatusOK {
    log.Printf("用户信息: %s", string(body))
    // 响应示例：
    // {
    //   "code": 200,
    //   "message": "用户查询成功",
    //   "data": {
    //     "user_name": "testuser",
    //     "user_id": "user_123456",
    //     // pass_wd字段已移除，不在响应中返回
    //     "user_type": 1,
    //     "gateway_device_id": "device_001",
    //     "email": "<EMAIL>",
    //     "auth_status": "normal",
    //     "login_status": "offline",
    //     "is_superior_forbidden": false,  // 上级设备冻结状态
    //     "created_at": "2025-01-20T10:30:00Z"
    //   }
    // }
} else {
    log.Printf("查询失败: %s", string(body))
}
```

### 9.3 查询位置信息

```go
// 发送GET请求查询位置信息
resp, err := http.Get("http://localhost:8080/api/get/location")
if err != nil {
    log.Fatalf("查询位置信息请求失败: %v", err)
}
defer resp.Body.Close()

// 解析响应
body, _ := io.ReadAll(resp.Body)
if resp.StatusCode == http.StatusOK {
    log.Printf("位置信息: %s", string(body))
} else {
    log.Printf("查询失败: %s", string(body))
}
```

### 9.4 查询事件记录

```go
// 发送GET请求查询事件记录
resp, err := http.Get("http://localhost:8080/api/get/events?start_time=2025-01-01%2000:00:00&end_time=2025-12-31%2023:59:59")
if err != nil {
    log.Fatalf("查询事件记录请求失败: %v", err)
}
defer resp.Body.Close()

// 解析响应
body, _ := io.ReadAll(resp.Body)
if resp.StatusCode == http.StatusOK {
    log.Printf("事件记录: %s", string(body))
} else {
    log.Printf("查询失败: %s", string(body))
}
```

### 9.5 按事件代码查询

```go
// 查询用户上线事件
resp, err := http.Get("http://localhost:8080/api/get/events?start_time=2025-01-01%2000:00:00&end_time=2025-12-31%2023:59:59&event_code=0101")
if err != nil {
    log.Fatalf("查询用户上线事件请求失败: %v", err)
}
defer resp.Body.Close()

// 解析响应
body, _ := io.ReadAll(resp.Body)
if resp.StatusCode == http.StatusOK {
    log.Printf("用户上线事件: %s", string(body))
} else {
    log.Printf("查询失败: %s", string(body))
}
```

## 10. 最新更新记录

### 10.1 上级设备冻结状态字段新增 (2025年6月更新)

#### 10.1.1 更新概述

为regist模块的用户查询和设备查询接口新增了`is_superior_forbidden`字段，用于标识当前设备/用户是否存在上级设备处于冻结状态。

#### 10.1.2 功能特性

**字段定义**：
- 字段名：`is_superior_forbidden`
- 数据类型：boolean
- 含义：标识当前设备/用户是否存在上级设备处于冻结状态

**业务逻辑**：
- 当安全接入管理设备（设备类型4）处于冻结状态时，其下属的所有网关设备和用户的`is_superior_forbidden`字段返回true
- 当网关设备处于冻结状态时，其下属的所有用户的`is_superior_forbidden`字段返回true
- 如果设备/用户的整个上级链路中没有冻结状态的设备，则返回false

**实现特点**：
- 动态计算字段，不存储到数据库中
- 支持多层级设备关系检查
- 性能优化，避免过多的数据库查询
- 防止循环引用的安全检查

#### 10.1.3 影响的接口

- `GET /search/user` - 单用户查询
- `GET /search/users` - 用户列表查询
- `GET /search/device` - 单设备查询
- `GET /search/devices` - 设备列表查询

#### 10.1.4 技术实现

**数据库仓库层扩展**：
- 在`DeviceRepository`中新增`CheckSuperiorDeviceFrozenStatus`方法
- 在`UserRepository`中新增`CheckGatewayDeviceFrozenStatus`方法

**响应结构体修改**：
- `UserResponse`结构体新增`is_superior_forbidden`字段
- `DeviceResponse`结构体新增`is_superior_forbidden`字段
- 更新相应的转换函数实现动态计算

### 10.2 API安全改进 - 敏感字段移除 (2025年1月更新)

#### 10.1.1 更新概述

完成了重要的API安全改进工作，移除了所有面向前端API响应中的敏感密码字段，显著提升了系统安全性。

#### 10.1.2 主要变更

**响应结构体修改**：
- 移除 `UserResponse` 结构体中的 `Password` 字段（`json:"pass_wd"`）
- 移除 `DeviceResponse` 结构体中的 `SESKey` 字段（`json:"ses_key"`）
- 更新相应的转换函数 `convertUserModelToResponse` 和 `convertDeviceModelToResponse`

**影响的接口**：
- 用户管理接口：`GET /search/users`、`GET /search/user`、`POST /regist/users`、`PUT /update/user`
- 设备管理接口：`GET /search/devices`、`GET /search/device`、`POST /regist/devices`、`PUT /update/device`

**保持不变的部分**：
- 数据库模型层面不做任何修改
- 配置文件生成相关接口保持原有功能
- 用户/设备认证接口保持原有功能

#### 10.1.3 安全收益

- ✅ 消除了API响应中的密码字段泄露风险
- ✅ 消除了API响应中的SES密钥泄露风险
- ✅ 符合安全最佳实践和最小权限原则
- ✅ 保持了所有业务功能的完整性
- ✅ 实现了向后兼容（除敏感字段外）

#### 10.1.4 相关文件

- `regist/handler/response.go`：响应结构体定义和转换函数
- `document/项目安全改进文档_API密码字段移除方案.md`：详细设计文档
- `document/API密码字段移除_执行清单.md`：实施指南
- `document/API接口影响分析表.md`：影响分析

### 10.2 位置查询接口增强 (2024年更新)

#### 10.2.1 更新内容

- **新增软件设备位置查询**：位置查询接口现在支持返回软件设备(device_type=4)的位置信息
- **三类设备分别展示**：接口返回数据现在包含 `user`、`gateway`、`software` 三个分类
- **数据库仓库层扩展**：新增 `FindSoftwareDevices` 方法支持软件设备查询

#### 10.2.2 技术实现

1. **数据库仓库层**：

   - 在 `DeviceRepository` 接口中新增 `FindSoftwareDevices` 方法
   - 实现查询 `device_type = 4` 的设备功能
2. **处理器层**：

   - 修改 `GetLocationData` 函数，新增软件设备查询逻辑
   - 重构 `buildLocationResponse` 函数，支持三类设备数据构建
   - 更新调试日志，包含软件设备数量信息

#### 10.2.3 接口变更

**原返回格式**：

```json
{
  "user": [...],
  "gateway": [...]
}
```

**新返回格式**：

```json
{
  "user": [...],
  "gateway": [...],
  "software": [...]
}
```

#### 10.2.4 兼容性说明

- **向后兼容**：原有 `user` 和 `gateway` 字段保持不变
- **新增字段**：`software` 字段为新增，不影响现有调用
- **性能影响**：仅新增一次数据库查询，性能影响微小

#### 10.2.5 相关文件

- `database/repositories/device_repository.go`：新增软件设备查询方法
- `regist/handler/location.go`：修改处理器逻辑和响应构建函数
- `regist/用户设备位置查询接口修改设计文档.md`：详细设计文档

### 10.3 用户/设备事件记录查询接口重构 (2025年更新)

#### 9.2.1 更新概述

将原有的用户/设备登录记录查询接口重构为更通用的事件记录查询接口，支持多种类型的操作事件，不仅限于登录/登出事件。

#### 9.2.2 主要变更

- **接口重命名**：从"登录记录查询接口"更名为"事件记录查询接口"
- **数据库表重构**：`login_events` 表更名为 `operation_events` 表
- **字段变更**：使用 `event_code` 字段替代 `operation_type` 字段
- **字段重命名**：`gateway_id` 更改为 `superior_device_id`
- **实体类型编码化**：`entity_type` 字段使用编码存储（"01"=用户，"02"=网关，"03"=边缘控制软件）
- **响应格式更新**：采用新的事件代码体系和响应结构

#### 9.2.3 事件代码体系

采用4位数字编码系统：

- **前两位**：事件类型（01=上线，02=下线，03=认证失败，04=发送数据，05=用户操作，06=接收数据，07=异常）
- **后两位**：主体身份（01=用户，02=网关，03=边缘控制软件）

#### 9.2.4 实体类型编码

| 实体类型     | 编码 | 说明             |
| ------------ | ---- | ---------------- |
| 用户         | "01" | 用户终端         |
| 网关         | "02" | 网关设备         |
| 边缘控制软件 | "03" | 安全接入管理设备 |

#### 9.2.5 技术实现

1. **数据库层**：

   - 创建 `operation_events` 表替代 `login_events` 表
   - 新增 `event_code` 字段支持4位事件代码
   - `entity_type` 字段使用编码存储（"01"、"02"、"03"）
   - 自动数据迁移和备份机制
2. **仓库层**：

   - 新增 `OperationEventRepository` 接口和实现
   - 支持多维度查询（时间范围、事件代码、实体类型）
   - 自动位置信息获取和响应格式化
3. **处理器层**：

   - 新增 `GetEventRecords` 处理函数
   - 完整的参数验证和错误处理
   - 保留 `GetLoginRecordsCompat` 兼容性函数
4. **路由层**：

   - 新增 `/api/get/events` 路由
   - 保留 `/api/get/login` 兼容性路由

#### 9.2.6 数据库变更

**新表结构**：

```sql
CREATE TABLE operation_events (
    id int(10) unsigned NOT NULL AUTO_INCREMENT,
    entity_type varchar(20) NOT NULL COMMENT '实体类型编码(01=用户,02=网关,03=边缘控制软件)',
    entity_id varchar(64) NOT NULL COMMENT '实体ID(用户ID或设备ID)',
    superior_device_id varchar(64) DEFAULT NULL COMMENT '上级设备ID，如果是安全接入管理设备则为空',
    event_code varchar(4) NOT NULL COMMENT '事件代码，4位数字字符串',
    operation_time datetime NOT NULL COMMENT '操作发生时间',
    login_result varchar(20) NOT NULL DEFAULT 'success' COMMENT '操作结果(success或failed)',
    created_at datetime NOT NULL,
    updated_at datetime NOT NULL,
    PRIMARY KEY (id),
    -- 多个索引用于优化查询性能
    KEY idx_entity_type (entity_type),
    KEY idx_entity_id (entity_id),
    KEY idx_superior_device_id (superior_device_id),
    KEY idx_event_code (event_code),
    KEY idx_operation_time (operation_time),
    KEY idx_entity_id_operation_time (entity_id, operation_time),
    KEY idx_superior_device_id_operation_time (superior_device_id, operation_time),
    KEY idx_entity_type_event_code (entity_type, event_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作事件记录表';
```

#### 9.2.7 接口响应格式

**新接口响应示例**：

```json
[
  {
    "event_code": "0101",
    "entity_id": "U00000001",
    "entity_type": "01",
    "superior_device_id": "G00000001",
    "location": "guangzhou",
    "operation_time": "2025-01-15T08:30:00+08:00"
  }
]
```

#### 9.2.8 相关文档

详细的设计文档请参考：

- `regist/用户设备事件记录查询接口重构设计文档.md`
- `ENTITY_TYPE_ENCODING_UPDATE.md`
- `OPERATION_EVENTS_IMPLEMENTATION.md`

### 10.4 设备就绪状态查询接口扩展 (2025年6月更新)

#### 9.3.1 更新概述

基于现有的设备搜索接口，新增了专门的设备就绪状态查询功能，提供更精确的设备状态监控能力。

#### 9.3.2 新增接口

1. **批量设备就绪状态查询接口** (`GET /search/devices/is_ready`)

   - ✅ 支持查询所有设备的就绪状态信息
   - ✅ 支持设备类型筛选（device_type: 1-4）
   - ✅ 支持就绪状态筛选（is_ready: true/false）
   - ✅ 支持分页查询（page, page_size）
   - ✅ 返回统计信息（总数、就绪数量、未就绪数量）
2. **单个设备就绪状态查询接口** (`GET /search/device/is_ready`)

   - ✅ 根据设备ID查询单个设备的就绪状态
   - ✅ 返回设备的详细就绪状态信息
   - ✅ 包含设备基本信息和状态信息

#### 9.3.3 技术实现特点

1. **代码复用性**：

   - 基于现有 `/search/devices` 和 `/search/device` 接口模式
   - 复用现有的参数验证和错误处理逻辑
   - 保持与现有接口相似的代码结构和风格
2. **参数验证完善**：

   - 设备类型范围验证（1-4）
   - 分页参数验证（page ≥ 1, page_size 1-100）
   - 就绪状态布尔值验证
   - 必需参数检查
3. **响应格式统一**：

   - 与现有接口保持一致的响应格式
   - 标准化的错误响应结构
   - ISO 8601时间格式输出
4. **性能优化**：

   - 使用数据库索引优化查询性能
   - 分页查询避免大量数据传输
   - 统计查询与列表查询分离

#### 9.3.4 数据库支持

- **设备表字段**：利用现有的 `is_ready` 字段进行状态查询
- **索引优化**：`is_ready` 字段已建立索引，支持高效筛选
- **类型支持**：支持所有设备类型（1-3:网关设备，4:安全接入管理设备）

#### 9.3.5 接口特性

1. **灵活筛选**：

   - 支持按设备类型筛选
   - 支持按就绪状态筛选
   - 支持组合条件查询
2. **分页支持**：

   - 默认分页大小：10
   - 最大分页大小：100
   - 返回分页元信息
3. **统计信息**：

   - 符合条件的设备总数
   - 就绪设备数量统计
   - 未就绪设备数量统计
4. **错误处理**：

   - 完整的参数验证
   - 标准化的错误响应
   - 适当的HTTP状态码

#### 9.3.6 相关文件

- `regist/handler/search.go`：新增处理函数和响应结构体
- `regist/router/router.go`：注册新路由
- `database/models/device.go`：设备模型包含 `is_ready` 字段
- `regist/regist.md`：更新接口文档

#### 9.3.7 使用示例

```bash
# 查询所有设备就绪状态
curl -X GET "http://localhost:8123/search/devices/is_ready?page=1&page_size=10"

# 按设备类型筛选
curl -X GET "http://localhost:8123/search/devices/is_ready?device_type=1&page_size=5"

# 按就绪状态筛选
curl -X GET "http://localhost:8123/search/devices/is_ready?is_ready=false"

# 查询单个设备就绪状态
curl -X GET "http://localhost:8123/search/device/is_ready?id=000000000001"
```

#### 9.3.8 兼容性说明

- **向后兼容**：新接口不影响现有接口功能
- **独立路由**：使用独立的路由路径，避免冲突
- **数据库兼容**：利用现有数据库字段，无需额外迁移

## 11. 设备就绪状态计算逻辑

### 11.1 is_ready 字段计算规则

**更新时间**: 2025年6月17日
**版本**: v3.0.0

为了更准确地反映设备的实际就绪状态，注册管理模块已更新设备就绪状态的计算逻辑。

#### 11.1.1 新的计算规则

**判断条件**：
- 当且仅当 `register_ip` 和 `short_address` 两个字段都不为空字符串且不为null时，设备状态为就绪（`is_ready = true`）
- 否则设备状态为未就绪（`is_ready = false`）

**字段说明**：
- `register_ip`: 设备注册IP地址，表示设备能够正常连接到系统
- `short_address`: 设备短地址，表示设备具备通信能力

#### 11.1.2 实现方式

**动态计算**：
- 所有返回设备信息的接口都会在响应前动态检查并更新 `is_ready` 状态
- 状态计算发生在运行时，不依赖数据库中存储的 `is_ready` 值
- 如果计算结果与数据库中的值不同，会自动更新数据库记录

**涉及的接口**：
- `GET /search/device` - 单个设备查询
- `GET /search/devices` - 设备列表查询
- `GET /search/devices/is_ready` - 设备就绪状态列表查询
- `GET /search/device/is_ready` - 单个设备就绪状态查询

#### 11.1.3 日志记录

系统会记录设备就绪状态的变更情况：

```
设备 [设备ID] 就绪状态已更新: false -> true (register_ip: *************, short_address: 0x1234)
```

**日志级别**：
- 状态更新成功：Debug 级别
- 状态更新失败：Important 级别

#### 11.1.4 技术实现

**核心函数**：
- `isDeviceReady(device *models.Device) bool`: 检查设备是否就绪
- `updateDeviceReadyStatus(device *models.Device, db *gorm.DB, log *logger.Logger)`: 动态更新设备就绪状态

**实现位置**：
- 文件：`regist/handler/search.go`
- 行号：第140-177行

#### 11.1.5 与旧版本的差异

**旧版本计算规则**（v2.x）：
- 基于三个字段：`short_address`、`long_address`、`ses_key`
- 要求三个字段都不为空才认为设备就绪

**新版本计算规则**（v3.0）：
- 基于两个字段：`register_ip`、`short_address`
- 更加注重设备的网络连接能力和基本通信能力

#### 11.1.6 业务意义

**更准确的状态判断**：
- `register_ip` 确保设备能够连接到系统
- `short_address` 确保设备具备基本的通信配置
- 移除对 `long_address` 和 `ses_key` 的依赖，简化判断逻辑

**实时状态更新**：
- 每次查询都会重新计算状态，确保返回最新的设备状态
- 自动同步数据库中的状态字段，保持数据一致性

## 12. API 接口字段修复记录

### 12.1 修复概述

**修复日期**：2025-06-17
**修复版本**：v3.1
**修复类型**：字段缺失修复

### 12.2 修复内容详情

#### 12.2.1 设备查询接口 is_superior_forbidden 字段修复

**问题描述**：
- 设备列表查询接口 (`GET /search/devices`) 缺少 `is_superior_forbidden` 字段
- 原因：接口直接返回数据库模型，未使用响应转换函数

**修复措施**：
- 修改 `GetDevices` 函数实现
- 使用 `convertDeviceModelToResponse` 函数进行响应转换
- 确保所有设备查询接口都包含 `is_superior_forbidden` 字段

**影响接口**：
- ✅ `GET /search/devices` - 设备列表查询（已修复）
- ✅ `GET /search/device` - 单个设备查询（原本正常）

**字段功能**：
- 动态计算设备的上级设备链路中是否存在 auth_status 为 "forbidden" 的设备
- 为前端提供设备状态判断依据

#### 12.2.2 用户接口 mac_address 字段修复

**问题描述**：
- 用户查询接口中 `mac_address` 字段使用了 `omitempty` JSON 标签
- 导致字段值为空时不在响应中显示

**修复措施**：
- 移除 `UserResponse` 结构体中 `mac_address` 字段的 `omitempty` 标签
- 确保字段始终在响应中显示，即使值为空

**影响接口**：
- ✅ `GET /search/users` - 用户列表查询（已修复）
- ✅ `GET /search/user` - 单个用户查询（已修复）
- ✅ `POST /regist/users` - 用户注册（原本支持）
- ✅ `PUT /update/user` - 用户更新（原本支持）

**字段功能**：
- 支持用户设备 MAC 地址的存储、验证和查询
- 提供 MAC 地址格式验证和唯一性检查

### 12.3 修复验证

**验证方法**：
- 创建自动化验证脚本
- 测试所有相关接口的字段返回情况
- 确认修复效果

**验证结果**：
- ✅ 设备列表查询接口包含 `is_superior_forbidden` 字段
- ✅ 设备单个查询接口包含 `is_superior_forbidden` 字段
- ✅ 用户列表查询接口包含 `mac_address` 字段
- ✅ 用户列表查询接口包含 `is_superior_forbidden` 字段
- ✅ 用户单个查询接口包含 `mac_address` 字段
- ✅ 用户单个查询接口包含 `is_superior_forbidden` 字段

### 12.4 技术实现细节

**代码修改位置**：
1. `regist/handler/device.go` - 第251-268行
2. `regist/handler/response.go` - 第31行

**修改内容**：
```go
// 修复前：直接返回数据库模型
c.JSON(http.StatusOK, gin.H{
    "code":    200,
    "message": "获取设备列表成功",
    "data":    devices,
})

// 修复后：使用响应转换函数
deviceResponses := make([]DeviceResponse, 0, len(devices))
for _, device := range devices {
    deviceResponses = append(deviceResponses, convertDeviceModelToResponse(&device, db))
}
c.JSON(http.StatusOK, gin.H{
    "code":    200,
    "message": "获取设备列表成功",
    "data":    deviceResponses,
})
```

```go
// 修复前：使用 omitempty 标签
MacAddress string `json:"mac_address,omitempty"`

// 修复后：移除 omitempty 标签
MacAddress string `json:"mac_address"`
```

### 12.5 兼容性保证

**向后兼容性**：
- ✅ 保持现有 API 接口的完全向后兼容性
- ✅ 新增字段不影响现有客户端的正常使用
- ✅ 响应格式和数据结构保持一致
- ✅ 错误处理和状态码保持不变

**数据一致性**：
- ✅ 所有接口返回的字段格式统一
- ✅ 动态计算字段逻辑保持一致
- ✅ 数据库字段映射正确

### 12.6 后续维护建议

**代码质量**：
- 建议在所有响应接口中统一使用响应转换函数
- 避免直接返回数据库模型到前端
- 定期检查 JSON 标签的使用是否符合业务需求

**测试覆盖**：
- 建议为所有 API 接口编写字段完整性测试
- 确保新增字段在所有相关接口中都能正确返回
- 定期进行接口兼容性测试
