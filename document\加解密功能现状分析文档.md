# 加解密功能现状分析文档

**分析日期**: 2025-07-11  
**分析范围**: configmanager/common/crypto/, configmanager/log/, configmanager/config_exec/  
**分析目标**: 全面梳理现有加解密功能，制定RSA和密钥生成功能删除计划

## 1. 总体现状概述

### 1.1 模块分布
- **crypto模块**: 核心加解密算法实现
- **log模块**: 日志文件加密功能
- **config_exec模块**: 配置文件解密功能
- **security模块**: 安全管理和密钥管理

### 1.2 加解密算法使用情况
- **对称加密**: AES-GCM (256/192/128位)
- **非对称加密**: RSA (1024/2048/4096位)、ECDSA (P256/P384/P521)、ED25519
- **密钥管理**: 自动生成、文件存储、权限控制

## 2. configmanager/common/crypto/ 模块详细分析

### 2.1 文件结构
```
crypto/
├── asymmetric.go      # 非对称加密实现 (需要删除)
├── crypto.go          # 核心加解密接口和AES实现 (部分保留)
└── keymanager.go      # 密钥管理器 (需要删除)
```

### 2.2 非对称加密实现 (asymmetric.go) - 需要完全删除

#### 2.2.1 RSA加密器 (RSAEncryptor)
- **位置**: asymmetric.go 第31-199行
- **功能**: RSA密钥生成、加解密、密钥文件保存/加载
- **关键方法**:
  - `NewRSAEncryptor()`: 创建RSA加密器
  - `GenerateKeyPair()`: 生成RSA密钥对
  - `EncryptKey()/DecryptKey()`: RSA加解密
  - `SavePublicKey()/SavePrivateKey()`: 密钥文件保存
  - `LoadPublicKey()/LoadPrivateKey()`: 密钥文件加载

#### 2.2.2 ECDSA加密器 (ECDSAEncryptor)
- **位置**: asymmetric.go 第200-330行
- **功能**: ECDSA密钥生成和管理
- **关键方法**:
  - `NewECDSAEncryptor()`: 创建ECDSA加密器
  - `GenerateKeyPair()`: 生成ECDSA密钥对
  - `EncryptKey()/DecryptKey()`: 使用临时RSA进行加解密

#### 2.2.3 ED25519加密器 (ED25519Encryptor)
- **位置**: asymmetric.go 第331-499行
- **功能**: ED25519密钥生成和管理
- **关键方法**:
  - `NewED25519Encryptor()`: 创建ED25519加密器
  - `GenerateKeyPair()`: 生成ED25519密钥对
  - `EncryptKey()/DecryptKey()`: 使用临时RSA进行加解密

#### 2.2.4 工厂方法
- **位置**: asymmetric.go 第487-499行
- **功能**: `CreateAsymmetricEncryptor()` 创建非对称加密器

### 2.3 对称加密实现 (crypto.go) - 需要保留

#### 2.3.1 AES加密器 (AESEncryptor) - 保留
- **位置**: crypto.go 第110-250行
- **功能**: AES-GCM对称加密
- **关键方法**:
  - `NewAESEncryptor()`: 创建AES加密器并生成密钥
  - `NewAESEncryptorWithKey()`: 使用指定密钥创建AES加密器
  - `Encrypt()/Decrypt()`: AES-GCM加解密
  - `GetKey()`: 获取AES密钥

#### 2.3.2 RSA公钥加密器 (RSAPublicKeyEncryptor) - 需要删除
- **位置**: crypto.go 第35-108行
- **功能**: 使用RSA公钥加密密钥
- **关键方法**:
  - `NewRSAPublicKeyEncryptor()`: 创建RSA公钥加密器
  - `EncryptKey()`: 使用RSA公钥加密密钥

### 2.4 密钥管理器 (keymanager.go) - 需要完全删除
- **位置**: keymanager.go 全文件
- **功能**: 非对称密钥对生成和管理
- **关键方法**:
  - `EnsureKeyPair()`: 确保密钥对存在，不存在则生成

## 3. configmanager/log/ 模块详细分析

### 3.1 加密功能使用情况

#### 3.1.1 日志加密器 (LogEncryptor)
- **位置**: service/encryptor.go
- **功能**: 处理日志文件加密
- **加密模式支持**:
  - ModeNone: 不加密
  - ModeSymmetric: 仅AES对称加密 (符合保留要求)
  - ModeBoth: AES + RSA混合加密 (需要删除)

#### 3.1.2 安全管理器集成
- **位置**: security.go, util.go
- **功能**: 通过security模块进行加密操作
- **依赖关系**: 依赖common/security模块的SecurityManager

#### 3.1.3 密钥管理
- **位置**: service/encryptor.go 第76-86行
- **功能**: AES密钥路径配置和管理
- **配置项**:
  - `EnableEncryption`: 是否启用加密
  - `AESKeyPath`: AES密钥文件路径
  - `AESKeyLength`: AES密钥长度

### 3.2 需要修改的部分
- **删除**: ModeBoth混合加密模式支持
- **保留**: ModeSymmetric对称加密模式
- **保留**: AES密钥管理功能

## 4. configmanager/config_exec/ 模块详细分析

### 4.1 解密功能使用情况

#### 4.1.1 配置文件处理器 (Processor)
- **位置**: controller/processor.go
- **功能**: 配置文件解密处理
- **关键方法**:
  - `DecryptFile()`: 解密配置文件内容
  - 使用security.GetDefaultConfigSecurityManager()获取安全管理器

#### 4.1.2 安全管理器集成
- **位置**: controller/processor.go 第48-51行
- **功能**: 通过security模块进行解密操作
- **依赖关系**: 依赖common/security模块

### 4.2 需要修改的部分
- **保留**: AES对称解密功能
- **删除**: RSA非对称解密相关配置和代码路径

## 5. configmanager/common/security/ 模块分析

### 5.1 安全管理器 (SecurityManager)
- **位置**: security.go
- **功能**: 统一的加解密接口
- **加密模式**:
  - ModeNone: 不加密
  - ModeSymmetric: 仅对称加密 (保留)
  - ModeBoth: 混合加密 (删除)

### 5.2 工具函数 (util.go)
- **功能**: 密钥生成和管理工具
- **关键方法**:
  - `GenerateSymmetricKey()`: 生成对称密钥 (保留)
  - `EnsureSymmetricKey()`: 确保对称密钥存在 (保留)

## 6. 模块间依赖关系分析

### 6.1 依赖链路
```
log模块 → security模块 → crypto模块
config_exec模块 → security模块 → crypto模块
```

### 6.2 关键依赖点
- **log模块**: 依赖security.GetDefaultLogSecurityManager()
- **config_exec模块**: 依赖security.GetDefaultConfigSecurityManager()
- **security模块**: 依赖crypto.NewAESEncryptor()和非对称加密器

## 7. 删除影响评估

### 7.1 需要删除的文件
- `configmanager/common/crypto/asymmetric.go` (完整删除)
- `configmanager/common/crypto/keymanager.go` (完整删除)

### 7.2 需要修改的文件
- `configmanager/common/crypto/crypto.go` (删除RSAPublicKeyEncryptor)
- `configmanager/common/security/security.go` (删除ModeBoth模式)
- `configmanager/common/security/util.go` (删除非对称加密相关配置)

### 7.3 配置项影响
- 删除所有RSA相关配置项
- 删除PublicKeyPath、PrivateKeyPath配置
- 保留AESKeyPath、AESKeyLength配置

## 8. 修改计划

### 8.1 第一阶段：删除非对称加密文件
1. 删除asymmetric.go文件
2. 删除keymanager.go文件
3. 修改crypto.go删除RSA相关代码

### 8.2 第二阶段：修改安全管理器
1. 删除ModeBoth加密模式
2. 简化SecurityManager接口
3. 更新配置管理器

### 8.3 第三阶段：测试验证
1. 编译测试
2. 功能测试
3. 集成测试

## 9. 详细修改计划

### 9.1 需要删除的具体代码文件

#### 9.1.1 完全删除的文件
```
configmanager/common/crypto/asymmetric.go     # 499行，包含所有非对称加密实现
configmanager/common/crypto/keymanager.go     # 包含密钥对生成管理
```

#### 9.1.2 需要修改的文件和具体位置
```
configmanager/common/crypto/crypto.go
├── 删除: RSAPublicKeyEncryptor结构体 (第35-108行)
├── 删除: NewRSAPublicKeyEncryptor函数
├── 删除: RSA相关导入包
└── 保留: AESEncryptor完整实现

configmanager/common/security/security.go
├── 删除: ModeBoth加密模式相关代码
├── 删除: encryptBoth/decryptBoth方法
├── 删除: 非对称加密相关配置字段
└── 保留: ModeSymmetric和ModeNone模式

configmanager/common/security/util.go
├── 删除: GetDefaultLogSecurityManager中的ModeBoth逻辑
├── 删除: GetDefaultConfigSecurityManager中的ModeBoth逻辑
├── 删除: PublicKeyPath/PrivateKeyPath相关配置
└── 保留: AES密钥管理功能
```

### 9.2 配置项变更计划

#### 9.2.1 需要删除的配置项
```yaml
# 以下配置项将被删除
config_manager:
  log_manager:
    encryption:
      public_key_path: ""      # 删除
      private_key_path: ""     # 删除
      public_key_algorithm: "" # 删除
      public_key_length: 0     # 删除

  config_file_manager:
    encryption:
      public_key_path: ""      # 删除
      private_key_path: ""     # 删除
```

#### 9.2.2 保留的配置项
```yaml
# 以下配置项将保留
config_manager:
  log_manager:
    enable_encryption: true
    aes_key_path: "keys/log/aes_key.bin"
    encryption:
      aes_key_length: 256

  config_file_manager:
    encryption:
      aes_key_path: "keys/config/aes_key.bin"
      aes_key_length: 256
```

### 9.3 代码修改示例

#### 9.3.1 security.go修改示例
```go
// 修改前的加密模式枚举
type EncryptionMode int
const (
    ModeNone EncryptionMode = iota
    ModeSymmetric
    ModeBoth  // 需要删除
)

// 修改后的加密模式枚举
type EncryptionMode int
const (
    ModeNone EncryptionMode = iota
    ModeSymmetric
    // ModeBoth 已删除
)

// 删除encryptBoth和decryptBoth方法
// 简化Encrypt和Decrypt方法的switch语句
```

#### 9.3.2 crypto.go修改示例
```go
// 删除以下导入
import (
    "crypto/rsa"     // 删除
    "crypto/x509"    // 删除
    "encoding/pem"   // 删除
)

// 删除RSAPublicKeyEncryptor结构体和相关方法
// 保留AESEncryptor完整实现
```

## 10. 执行步骤详细说明

### 10.1 准备阶段
1. **备份当前代码**: 确保可以回滚
2. **确认依赖关系**: 检查是否有其他模块依赖要删除的代码
3. **准备测试环境**: 确保修改后可以进行完整测试

### 10.2 执行阶段
1. **第一步**: 删除asymmetric.go和keymanager.go文件
2. **第二步**: 修改crypto.go删除RSA相关代码
3. **第三步**: 修改security.go删除ModeBoth模式
4. **第四步**: 更新util.go中的配置管理逻辑
5. **第五步**: 清理未使用的导入包

### 10.3 验证阶段
1. **编译测试**: 确保代码可以正常编译
2. **单元测试**: 测试AES加解密功能
3. **集成测试**: 测试log和config_exec模块功能
4. **配置测试**: 验证配置文件加载和解析

## 11. 风险评估和缓解措施

### 11.1 编译风险
- **风险**: 删除文件后可能存在未发现的依赖
- **缓解**: 逐步删除，每步都进行编译测试

### 11.2 功能风险
- **风险**: 误删除对称加密相关代码
- **缓解**: 仔细审查每个删除操作，确保只删除非对称加密

### 11.3 配置风险
- **风险**: 现有配置文件可能包含RSA相关配置
- **缓解**: 提供配置迁移指南，支持向后兼容

## 12. 预期结果

### 12.1 代码简化
- 删除约500+行非对称加密相关代码
- 简化安全管理器接口
- 减少配置复杂度

### 12.2 功能保留
- 完整保留AES对称加密功能
- 保留所有密钥管理功能
- 保留现有的加密/解密接口

### 12.3 性能提升
- 减少不必要的密钥生成操作
- 简化加密模式判断逻辑
- 降低内存占用
